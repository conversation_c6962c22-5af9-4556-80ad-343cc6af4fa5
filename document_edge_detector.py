#!/usr/bin/env python3
"""
Adobe Scan 文档边缘检测 Python 实现
基于逆向工程提取的 TensorFlow Lite 模型

主要功能：
- 加载 Adobe Scan 的 capture_6_22_23.tflite 模型
- 检测图像中的文档边缘
- 返回4个角点的归一化坐标
- 可视化检测结果

技术规格：
- 输入：640x480 灰度图像
- 输出：4个角点坐标 (归一化到 0-1 范围)
- 模型大小：2.42 MB
- 推理时间：~50ms (取决于硬件)
"""

import cv2
import numpy as np
import tensorflow as tf
import argparse
import os
import time
from typing import Tuple, List, Optional
import matplotlib.pyplot as plt


class AdobeScanDocumentDetector:
    """Adobe Scan 文档边缘检测器"""
    
    def __init__(self, model_path: str = "FindDocEdgeDemo/app/src/main/assets/models/capture_6_22_23.tflite"):
        """
        初始化文档检测器
        
        Args:
            model_path: Adobe Scan 模型文件路径
        """
        self.model_path = model_path
        self.interpreter = None
        self.input_details = None
        self.output_details = None
        self.input_shape = (320, 320)  # 根据实际模型规格
        
        self._load_model()
    
    def _load_model(self):
        """加载 TensorFlow Lite 模型"""
        try:
            if not os.path.exists(self.model_path):
                raise FileNotFoundError(f"模型文件不存在: {self.model_path}")
            
            print(f"正在加载 Adobe Scan 模型: {self.model_path}")
            
            # 加载模型
            self.interpreter = tf.lite.Interpreter(model_path=self.model_path)
            self.interpreter.allocate_tensors()
            
            # 获取输入输出信息
            self.input_details = self.interpreter.get_input_details()
            self.output_details = self.interpreter.get_output_details()
            
            print("模型加载成功!")
            print(f"输入形状: {self.input_details[0]['shape']}")
            print(f"输出形状: {self.output_details[0]['shape']}")
            print(f"输入数据类型: {self.input_details[0]['dtype']}")
            print(f"输出数据类型: {self.output_details[0]['dtype']}")
            
        except Exception as e:
            print(f"模型加载失败: {e}")
            raise
    
    def preprocess_image(self, image: np.ndarray) -> np.ndarray:
        """
        图像预处理，按照 Adobe Scan 的实际规格

        Args:
            image: 输入图像 (BGR 格式)

        Returns:
            预处理后的图像数组 [1, 320, 320, 4]
        """
        # 调整大小到 320x320
        resized = cv2.resize(image, self.input_shape)

        # 确保是3通道BGR图像
        if len(resized.shape) == 2:
            resized = cv2.cvtColor(resized, cv2.COLOR_GRAY2BGR)
        elif resized.shape[2] == 4:
            resized = cv2.cvtColor(resized, cv2.COLOR_BGRA2BGR)

        # 模型期望4通道输入，添加alpha通道
        alpha_channel = np.ones((320, 320, 1), dtype=np.uint8) * 255
        rgba_image = np.concatenate([resized, alpha_channel], axis=2)

        # 添加批次维度: (320, 320, 4) -> (1, 320, 320, 4)
        processed = np.expand_dims(rgba_image, axis=0)

        return processed
    
    def detect_document_edges(self, image: np.ndarray) -> Optional[np.ndarray]:
        """
        检测文档边缘
        
        Args:
            image: 输入图像
            
        Returns:
            4个角点的归一化坐标 [(x1,y1), (x2,y2), (x3,y3), (x4,y4)]
            坐标范围 0-1，如果检测失败返回 None
        """
        if self.interpreter is None:
            raise RuntimeError("模型未加载")
        
        try:
            # 预处理图像
            processed_image = self.preprocess_image(image)
            
            # 设置输入
            self.interpreter.set_tensor(self.input_details[0]['index'], processed_image)
            
            # 运行推理
            start_time = time.time()
            self.interpreter.invoke()
            inference_time = (time.time() - start_time) * 1000
            
            # 获取输出
            output_data = self.interpreter.get_tensor(self.output_details[0]['index'])
            
            print(f"推理时间: {inference_time:.2f} ms")
            print(f"输出形状: {output_data.shape}")
            print(f"输出数据: {output_data}")
            
            # 解析输出 - 实际模型输出13个点，需要智能选择4个角点
            if output_data.size >= 26:  # 13个点，每个点2个坐标
                # 重塑为 (13, 2) 格式
                all_points = output_data.reshape(-1, 2)

                print(f"检测到 {len(all_points)} 个点:")
                for i, (x, y) in enumerate(all_points):
                    print(f"  点 {i+1}: ({x:.4f}, {y:.4f})")

                # 尝试智能选择4个角点
                corners = self._select_corner_points(all_points)

                # 确保坐标在 0-1 范围内
                corners = np.clip(corners, 0.0, 1.0)

                print(f"选择的4个角点:")
                for i, (x, y) in enumerate(corners):
                    print(f"  角点 {i+1}: ({x:.4f}, {y:.4f})")

                return corners
            else:
                print(f"输出数据格式不符合预期，期望26个值，实际得到{output_data.size}个")
                return None
                
        except Exception as e:
            print(f"检测失败: {e}")
            return None

    def _select_corner_points(self, all_points):
        """
        从13个检测点中智能选择4个角点
        使用强制象限分布策略解决点聚集问题
        """
        if len(all_points) < 4:
            return all_points[:4] if len(all_points) >= 4 else None

        print(f"\n[角点选择] 开始从{len(all_points)}个点中选择4个角点")

        # 分析点的分布
        self._analyze_point_distribution(all_points)

        # 优先使用强制象限分布方法
        corners_quadrant = self._method_forced_quadrant_selection(all_points)
        if corners_quadrant is not None and len(corners_quadrant) == 4:
            print("[最终选择] 使用强制象限分布方法")
            return corners_quadrant

        # 备选方法：智能分散选择
        corners_dispersed = self._method_intelligent_dispersed_selection(all_points)
        if corners_dispersed is not None and len(corners_dispersed) == 4:
            print("[最终选择] 使用智能分散选择方法")
            return corners_dispersed

        # 最后备选：改进的极值点选择
        print("[最终选择] 使用改进的极值点方法")
        return self._method_improved_extreme_points(all_points)

    def _analyze_point_distribution(self, all_points):
        """分析点的分布情况"""
        x_coords = all_points[:, 0]
        y_coords = all_points[:, 1]

        # 统计各象限的点数
        q1 = np.sum((x_coords > 0.5) & (y_coords < 0.5))  # 右上
        q2 = np.sum((x_coords < 0.5) & (y_coords < 0.5))  # 左上
        q3 = np.sum((x_coords < 0.5) & (y_coords > 0.5))  # 左下
        q4 = np.sum((x_coords > 0.5) & (y_coords > 0.5))  # 右下

        print(f"[分布分析] 右上象限: {q1}个, 左上象限: {q2}个, 左下象限: {q3}个, 右下象限: {q4}个")

        if q1 > len(all_points) * 0.6:
            print("[分布分析] ⚠️  检测到点聚集问题：大部分点集中在右上象限")

        return q1, q2, q3, q4

    def _method_forced_quadrant_selection(self, all_points):
        """强制象限分布选择方法"""
        print("[方法] 尝试强制象限分布选择...")

        def select_from_quadrant(points, x_thresh=0.5, y_thresh=0.5, target_x='high', target_y='low'):
            if target_x == 'high' and target_y == 'low':  # 右上
                mask = (points[:, 0] > x_thresh) & (points[:, 1] < y_thresh)
                quadrant_name = "右上"
            elif target_x == 'low' and target_y == 'low':  # 左上
                mask = (points[:, 0] < x_thresh) & (points[:, 1] < y_thresh)
                quadrant_name = "左上"
            elif target_x == 'low' and target_y == 'high':  # 左下
                mask = (points[:, 0] < x_thresh) & (points[:, 1] > y_thresh)
                quadrant_name = "左下"
            else:  # 右下
                mask = (points[:, 0] > x_thresh) & (points[:, 1] > y_thresh)
                quadrant_name = "右下"

            if np.any(mask):
                candidates = points[mask]
                # 选择最极端的点
                if target_x == 'high' and target_y == 'low':  # 右上
                    idx = np.argmax(candidates[:, 0] - candidates[:, 1])
                elif target_x == 'low' and target_y == 'low':  # 左上
                    idx = np.argmin(candidates[:, 0] + candidates[:, 1])
                elif target_x == 'low' and target_y == 'high':  # 左下
                    idx = np.argmin(candidates[:, 0] - candidates[:, 1])
                else:  # 右下
                    idx = np.argmax(candidates[:, 0] + candidates[:, 1])

                selected_point = candidates[idx]
                print(f"  {quadrant_name}象限选择: ({selected_point[0]:.4f}, {selected_point[1]:.4f})")
                return selected_point
            else:
                print(f"  {quadrant_name}象限: 无可用点")
                return None

        # 尝试从每个象限选择点
        top_left = select_from_quadrant(all_points, target_x='low', target_y='low')
        top_right = select_from_quadrant(all_points, target_x='high', target_y='low')
        bottom_right = select_from_quadrant(all_points, target_x='high', target_y='high')
        bottom_left = select_from_quadrant(all_points, target_x='low', target_y='high')

        # 收集有效的角点
        corners = []
        if top_left is not None:
            corners.append(top_left)
        if top_right is not None:
            corners.append(top_right)
        if bottom_right is not None:
            corners.append(bottom_right)
        if bottom_left is not None:
            corners.append(bottom_left)

        if len(corners) >= 3:  # 至少需要3个不同象限的点
            # 如果只有3个点，补充第4个点
            if len(corners) == 3:
                # 找到距离已选点最远的点
                remaining_points = []
                for point in all_points:
                    is_selected = False
                    for corner in corners:
                        if np.allclose(point, corner, atol=1e-6):
                            is_selected = True
                            break
                    if not is_selected:
                        remaining_points.append(point)

                if remaining_points:
                    remaining_points = np.array(remaining_points)
                    max_min_dist = -1
                    best_point = None

                    for point in remaining_points:
                        min_dist = min(np.sqrt(np.sum((point - corner)**2)) for corner in corners)
                        if min_dist > max_min_dist:
                            max_min_dist = min_dist
                            best_point = point

                    if best_point is not None:
                        corners.append(best_point)
                        print(f"  补充第4个点: ({best_point[0]:.4f}, {best_point[1]:.4f})")

            return np.array(corners[:4])

        return None

    def _method_intelligent_dispersed_selection(self, all_points):
        """智能分散选择方法"""
        print("[方法] 尝试智能分散选择...")

        # 贪心算法选择最分散的4个点
        selected_indices = []

        # 第一个点：选择最接近左上角的点
        distances_to_top_left = np.sqrt(all_points[:, 0]**2 + all_points[:, 1]**2)
        first_idx = np.argmin(distances_to_top_left)
        selected_indices.append(first_idx)
        print(f"  第1个点: ({all_points[first_idx][0]:.4f}, {all_points[first_idx][1]:.4f})")

        # 后续点：选择与已选点距离最大的点
        for i in range(1, 4):
            max_min_dist = -1
            best_idx = -1

            for j, point in enumerate(all_points):
                if j in selected_indices:
                    continue

                # 计算到已选点的最小距离
                min_dist = min(np.sqrt(np.sum((point - all_points[idx])**2))
                              for idx in selected_indices)

                if min_dist > max_min_dist:
                    max_min_dist = min_dist
                    best_idx = j

            if best_idx != -1:
                selected_indices.append(best_idx)
                print(f"  第{i+1}个点: ({all_points[best_idx][0]:.4f}, {all_points[best_idx][1]:.4f})")

        if len(selected_indices) == 4:
            return all_points[selected_indices]
        return None

    def _method_improved_extreme_points(self, all_points):
        """改进的极值点选择方法"""
        print("[方法] 使用改进的极值点选择...")

        # 找到各个方向的极值点
        min_x_idx = np.argmin(all_points[:, 0])
        max_x_idx = np.argmax(all_points[:, 0])
        min_y_idx = np.argmin(all_points[:, 1])
        max_y_idx = np.argmax(all_points[:, 1])

        # 找到对角线极值点
        sum_coords = all_points[:, 0] + all_points[:, 1]
        diff_coords = all_points[:, 0] - all_points[:, 1]
        min_sum_idx = np.argmin(sum_coords)  # 左上方向
        max_sum_idx = np.argmax(sum_coords)  # 右下方向
        min_diff_idx = np.argmin(diff_coords)  # 左下方向
        max_diff_idx = np.argmax(diff_coords)  # 右上方向

        # 收集所有候选点
        candidates_idx = [min_x_idx, max_x_idx, min_y_idx, max_y_idx,
                         min_sum_idx, max_sum_idx, min_diff_idx, max_diff_idx]

        # 去重
        unique_candidates_idx = list(set(candidates_idx))

        if len(unique_candidates_idx) >= 4:
            # 选择最分散的4个点
            candidate_points = all_points[unique_candidates_idx]
            selected = self._select_most_dispersed_4_points(candidate_points)
            print(f"  从{len(unique_candidates_idx)}个候选点中选择了4个最分散的点")
            return selected
        else:
            # 如果候选点不足4个，补充其他点
            remaining_indices = [i for i in range(len(all_points)) if i not in unique_candidates_idx]
            while len(unique_candidates_idx) < 4 and remaining_indices:
                unique_candidates_idx.append(remaining_indices.pop(0))

            return all_points[unique_candidates_idx[:4]]

    def _method_convex_hull(self, all_points):
        """方法1: 使用凸包算法"""
        from scipy.spatial import ConvexHull
        hull = ConvexHull(all_points)
        hull_points = all_points[hull.vertices]

        if len(hull_points) >= 4:
            return self._select_best_4_corners(hull_points)
        return None

    def _method_distance_based(self, all_points):
        """方法2: 基于距离的选择"""
        corners_ideal = np.array([
            [0.0, 0.0],  # 左上
            [1.0, 0.0],  # 右上
            [1.0, 1.0],  # 右下
            [0.0, 1.0]   # 左下
        ])

        selected_corners = []
        used_indices = set()

        for i, ideal_corner in enumerate(corners_ideal):
            min_distance = float('inf')
            best_idx = -1

            for j, point in enumerate(all_points):
                if j in used_indices:
                    continue

                distance = np.sqrt(np.sum((point - ideal_corner) ** 2))

                # 角落位置权重调整
                if (ideal_corner[0] == 0 or ideal_corner[0] == 1) and \
                   (ideal_corner[1] == 0 or ideal_corner[1] == 1):
                    distance *= 0.8

                if distance < min_distance:
                    min_distance = distance
                    best_idx = j

            if best_idx != -1:
                selected_corners.append(all_points[best_idx])
                used_indices.add(best_idx)

        return np.array(selected_corners) if len(selected_corners) == 4 else None

    def _method_extreme_points(self, all_points):
        """方法3: 极值点选择"""
        # 找到各个方向的极值点
        min_x_idx = np.argmin(all_points[:, 0])
        max_x_idx = np.argmax(all_points[:, 0])
        min_y_idx = np.argmin(all_points[:, 1])
        max_y_idx = np.argmax(all_points[:, 1])

        # 找到对角线极值点
        sum_coords = all_points[:, 0] + all_points[:, 1]
        diff_coords = all_points[:, 0] - all_points[:, 1]
        min_sum_idx = np.argmin(sum_coords)  # 左上方向
        max_sum_idx = np.argmax(sum_coords)  # 右下方向
        min_diff_idx = np.argmin(diff_coords)  # 左下方向
        max_diff_idx = np.argmax(diff_coords)  # 右上方向

        # 收集所有候选点
        candidates = [min_x_idx, max_x_idx, min_y_idx, max_y_idx,
                     min_sum_idx, max_sum_idx, min_diff_idx, max_diff_idx]

        # 去重并选择最分散的4个点
        unique_candidates = list(set(candidates))

        if len(unique_candidates) >= 4:
            # 选择最分散的4个点
            candidate_points = all_points[unique_candidates]
            return self._select_most_dispersed_4_points(candidate_points)

        return None

    def _method_clustering(self, all_points):
        """方法4: 聚类方法"""
        try:
            from sklearn.cluster import KMeans

            # 使用4个聚类中心
            kmeans = KMeans(n_clusters=4, random_state=42, n_init=10)
            labels = kmeans.fit_predict(all_points)

            # 每个聚类选择最接近质心的点
            selected_corners = []
            for i in range(4):
                cluster_points = all_points[labels == i]
                if len(cluster_points) > 0:
                    # 选择最接近聚类中心的点
                    center = kmeans.cluster_centers_[i]
                    distances = np.sqrt(np.sum((cluster_points - center) ** 2, axis=1))
                    best_idx = np.argmin(distances)
                    selected_corners.append(cluster_points[best_idx])

            return np.array(selected_corners) if len(selected_corners) == 4 else None

        except ImportError:
            return None

    def _select_most_dispersed_4_points(self, points):
        """从候选点中选择最分散的4个点"""
        if len(points) == 4:
            return points

        # 计算所有点对之间的距离
        n = len(points)
        distances = np.zeros((n, n))
        for i in range(n):
            for j in range(i+1, n):
                dist = np.sqrt(np.sum((points[i] - points[j]) ** 2))
                distances[i, j] = distances[j, i] = dist

        # 贪心算法选择最分散的4个点
        selected = [0]  # 从第一个点开始

        for _ in range(3):
            max_min_dist = -1
            best_candidate = -1

            for candidate in range(n):
                if candidate in selected:
                    continue

                # 计算候选点到已选点的最小距离
                min_dist = min(distances[candidate, s] for s in selected)

                if min_dist > max_min_dist:
                    max_min_dist = min_dist
                    best_candidate = candidate

            if best_candidate != -1:
                selected.append(best_candidate)

        return points[selected]

    def _evaluate_corner_quality(self, methods_results):
        """评估不同方法的角点质量"""
        scores = {}

        for method_name, corners in methods_results.items():
            score = 0

            # 评估1: 角点分布的均匀性
            if len(corners) == 4:
                # 计算角点形成的四边形面积
                area = self._calculate_quadrilateral_area(corners)
                score += area * 100  # 面积越大越好

                # 计算角点间距离的标准差（越小越好，说明分布越均匀）
                distances = []
                for i in range(4):
                    for j in range(i+1, 4):
                        dist = np.sqrt(np.sum((corners[i] - corners[j]) ** 2))
                        distances.append(dist)

                if distances:
                    dist_std = np.std(distances)
                    score += (1.0 - dist_std) * 50  # 标准差越小分数越高

                # 评估3: 角点是否接近图像边界
                boundary_score = 0
                for corner in corners:
                    # 计算到最近边界的距离
                    dist_to_boundary = min(corner[0], corner[1],
                                         1.0 - corner[0], 1.0 - corner[1])
                    boundary_score += (1.0 - dist_to_boundary)  # 越接近边界分数越高

                score += boundary_score * 25

            scores[method_name] = score
            print(f"[评估] {method_name}: {score:.2f}")

        # 返回得分最高的方法
        return max(scores.keys(), key=lambda k: scores[k])

    def _calculate_quadrilateral_area(self, corners):
        """计算四边形面积"""
        if len(corners) != 4:
            return 0

        # 使用鞋带公式计算多边形面积
        x = corners[:, 0]
        y = corners[:, 1]

        # 确保按顺序排列
        center = np.mean(corners, axis=0)
        angles = np.arctan2(y - center[1], x - center[0])
        sorted_indices = np.argsort(angles)

        sorted_corners = corners[sorted_indices]
        x_sorted = sorted_corners[:, 0]
        y_sorted = sorted_corners[:, 1]

        area = 0.5 * abs(sum(x_sorted[i] * y_sorted[(i + 1) % 4] -
                           x_sorted[(i + 1) % 4] * y_sorted[i] for i in range(4)))

        return area

    def _select_best_4_corners(self, hull_points):
        """
        从凸包点中选择最佳的4个角点
        """
        if len(hull_points) == 4:
            return hull_points

        # 如果凸包点多于4个，选择最极端的4个点
        # 计算每个点到中心的距离和角度
        center = np.mean(hull_points, axis=0)

        # 计算角度
        angles = []
        for point in hull_points:
            angle = np.arctan2(point[1] - center[1], point[0] - center[0])
            angles.append(angle)

        # 按角度排序
        sorted_indices = np.argsort(angles)

        # 选择4个最分散的点
        if len(hull_points) > 4:
            # 选择角度最分散的4个点
            step = len(hull_points) // 4
            selected_indices = [sorted_indices[i * step] for i in range(4)]
            return hull_points[selected_indices]

        return hull_points

    def _select_extreme_points(self, all_points):
        """
        备选方法：选择极值点作为角点
        """
        # 找到x和y坐标的极值点
        min_x_idx = np.argmin(all_points[:, 0])
        max_x_idx = np.argmax(all_points[:, 0])
        min_y_idx = np.argmin(all_points[:, 1])
        max_y_idx = np.argmax(all_points[:, 1])

        # 选择4个不同的极值点
        extreme_indices = list(set([min_x_idx, max_x_idx, min_y_idx, max_y_idx]))

        # 如果极值点少于4个，补充其他点
        while len(extreme_indices) < 4 and len(extreme_indices) < len(all_points):
            for i in range(len(all_points)):
                if i not in extreme_indices:
                    extreme_indices.append(i)
                    break

        # 取前4个点
        selected_indices = extreme_indices[:4]
        return all_points[selected_indices]
    
    def visualize_detection(self, image: np.ndarray, corners: np.ndarray, 
                          save_path: Optional[str] = None) -> np.ndarray:
        """
        可视化检测结果
        
        Args:
            image: 原始图像
            corners: 检测到的角点 (归一化坐标)
            save_path: 保存路径 (可选)
            
        Returns:
            绘制了检测结果的图像
        """
        result_image = image.copy()
        h, w = image.shape[:2]
        
        # 将归一化坐标转换为像素坐标
        pixel_corners = corners * np.array([w, h])
        pixel_corners = pixel_corners.astype(np.int32)
        
        # 绘制角点
        for i, (x, y) in enumerate(pixel_corners):
            cv2.circle(result_image, (x, y), 8, (0, 255, 0), -1)
            cv2.putText(result_image, f"{i+1}", (x+10, y-10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
        
        # 绘制边缘线
        if len(pixel_corners) == 4:
            # 连接角点形成文档边框
            pts = pixel_corners.reshape((-1, 1, 2))
            cv2.polylines(result_image, [pts], True, (0, 255, 255), 3)
        
        # 添加检测信息
        cv2.putText(result_image, "Adobe Scan Document Detection", 
                   (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        cv2.putText(result_image, f"Corners: {len(corners)}", 
                   (10, 70), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        
        # 保存结果
        if save_path:
            cv2.imwrite(save_path, result_image)
            print(f"检测结果已保存到: {save_path}")
        
        return result_image


def create_test_document_image(width=800, height=600, save_path="test_document.jpg"):
    """
    创建一个测试文档图像，包含清晰的边缘
    """
    # 创建白色背景
    image = np.ones((height, width, 3), dtype=np.uint8) * 255

    # 添加一些噪声背景
    noise = np.random.randint(240, 255, (height, width, 3), dtype=np.uint8)
    image = cv2.addWeighted(image, 0.8, noise, 0.2, 0)

    # 定义文档区域（稍微倾斜的矩形）
    doc_corners = np.array([
        [100, 80],    # 左上
        [700, 60],    # 右上
        [720, 520],   # 右下
        [80, 540]     # 左下
    ], dtype=np.float32)

    # 创建文档内容区域
    doc_mask = np.zeros((height, width), dtype=np.uint8)
    cv2.fillPoly(doc_mask, [doc_corners.astype(np.int32)], 255)

    # 文档内容（白色）
    document_content = np.ones((height, width, 3), dtype=np.uint8) * 250

    # 添加一些文本线条模拟
    for i in range(10):
        y = 120 + i * 35
        if y < 500:
            cv2.line(document_content, (120, y), (680, y), (200, 200, 200), 2)

    # 应用文档区域
    for c in range(3):
        image[:, :, c] = np.where(doc_mask > 0, document_content[:, :, c], image[:, :, c])

    # 添加边缘阴影效果
    shadow_mask = cv2.dilate(doc_mask, np.ones((10, 10), np.uint8), iterations=1)
    shadow_mask = cv2.GaussianBlur(shadow_mask, (15, 15), 0)
    shadow_mask = shadow_mask.astype(np.float32) / 255.0

    for c in range(3):
        image[:, :, c] = (image[:, :, c] * (0.7 + 0.3 * shadow_mask)).astype(np.uint8)

    # 保存图像
    cv2.imwrite(save_path, image)
    print(f"测试文档图像已创建: {save_path}")
    print(f"真实角点坐标: {doc_corners}")

    return image, doc_corners


def main():
    """主函数 - 命令行接口"""
    parser = argparse.ArgumentParser(description="Adobe Scan 文档边缘检测")
    parser.add_argument("--image", "-i", help="输入图像路径")
    parser.add_argument("--model", "-m",
                       default="FindDocEdgeDemo/app/src/main/assets/models/capture_6_22_23.tflite",
                       help="模型文件路径")
    parser.add_argument("--output", "-o", help="输出图像路径")
    parser.add_argument("--show", "-s", action="store_true", help="显示结果")
    parser.add_argument("--create-test", action="store_true", help="创建测试文档图像")

    args = parser.parse_args()

    # 如果请求创建测试图像
    if args.create_test:
        test_image, true_corners = create_test_document_image()
        args.image = "test_document.jpg"
        if not args.output:
            args.output = "test_document_result.jpg"
        print(f"真实角点坐标 (像素): {true_corners}")
        print(f"真实角点坐标 (归一化): {true_corners / [800, 600]}")

    if not args.image:
        print("请提供输入图像路径或使用 --create-test 创建测试图像")
        return

    # 检查输入文件
    if not os.path.exists(args.image):
        print(f"错误: 图像文件不存在 - {args.image}")
        return
    
    # 初始化检测器
    try:
        detector = AdobeScanDocumentDetector(args.model)
    except Exception as e:
        print(f"初始化失败: {e}")
        return
    
    # 加载图像
    print(f"正在处理图像: {args.image}")
    image = cv2.imread(args.image)
    if image is None:
        print("错误: 无法加载图像")
        return
    
    print(f"图像尺寸: {image.shape}")
    
    # 检测文档边缘
    corners = detector.detect_document_edges(image)
    
    if corners is not None:
        print("检测成功!")
        print("检测到的角点坐标 (归一化):")
        for i, (x, y) in enumerate(corners):
            print(f"  角点 {i+1}: ({x:.4f}, {y:.4f})")
        
        # 可视化结果
        result_image = detector.visualize_detection(image, corners, args.output)
        
        # 显示结果
        if args.show:
            cv2.imshow("Adobe Scan Document Detection", result_image)
            print("按任意键关闭窗口...")
            cv2.waitKey(0)
            cv2.destroyAllWindows()
    else:
        print("检测失败: 未能检测到文档边缘")


if __name__ == "__main__":
    main()
