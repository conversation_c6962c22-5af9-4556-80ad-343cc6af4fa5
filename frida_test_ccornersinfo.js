/**
 * 简化版Frida脚本：专门测试CCornersInfo类的Hook
 * 用于验证修复后的Hook是否正常工作
 */

console.log("[+] CCornersInfo测试脚本启动");

// 全局变量存储检测数据
let detectionData = {
    cornerResults: [],
    totalCount: 0
};

// Hook CCornersInfo 相关函数
function hookCCornersInfo() {
    console.log("[+] 开始Hook CCornersInfo...");
    
    Java.perform(function() {
        try {
            let CCornersInfo = Java.use("com.adobe.magic_clean.CCornersInfo");
            console.log("[+] 成功获取CCornersInfo类");
            
            // Hook 无参构造函数
            try {
                CCornersInfo.$init.overload().implementation = function() {
                    console.log("[CCornersInfo] 创建无参构造函数实例");
                    detectionData.totalCount++;
                    return this.$init();
                };
                console.log("[+] Hook无参构造函数成功");
            } catch (e) {
                console.log("[!] Hook无参构造函数失败:", e);
            }
            
            // Hook 两参数构造函数 (CCornersInfoType, PointF[])
            try {
                CCornersInfo.$init.overload('com.adobe.magic_clean.CCornersInfo$CCornersInfoType', '[Landroid.graphics.PointF;').implementation = function(type, points) {
                    console.log("[CCornersInfo] 创建两参数构造函数实例");
                    console.log("  类型:", type.toString());
                    console.log("  点数组长度:", points ? points.length : 0);
                    
                    detectionData.totalCount++;
                    
                    // 打印角点坐标
                    if (points && points.length > 0) {
                        console.log("  角点坐标:");
                        for (let i = 0; i < points.length; i++) {
                            let point = points[i];
                            if (point) {
                                let x = point.x.value;
                                let y = point.y.value;
                                console.log(`    点${i + 1}: (${x.toFixed(4)}, ${y.toFixed(4)})`);
                            }
                        }
                        
                        // 存储数据
                        let cornerData = [];
                        for (let i = 0; i < points.length; i++) {
                            if (points[i]) {
                                cornerData.push({
                                    x: points[i].x.value,
                                    y: points[i].y.value
                                });
                            }
                        }
                        
                        detectionData.cornerResults.push({
                            timestamp: Date.now(),
                            type: type.toString(),
                            corners: cornerData,
                            cornerCount: points.length,
                            constructorType: "two_param"
                        });
                        
                        console.log(`[数据] 已存储第${detectionData.cornerResults.length}个检测结果`);
                    }
                    
                    return this.$init(type, points);
                };
                console.log("[+] Hook两参数构造函数成功");
            } catch (e) {
                console.log("[!] Hook两参数构造函数失败:", e);
            }
            
            // Hook 三参数构造函数 (CCornersInfoType, PointF[], boolean)
            try {
                CCornersInfo.$init.overload('com.adobe.magic_clean.CCornersInfo$CCornersInfoType', '[Landroid.graphics.PointF;', 'boolean').implementation = function(type, points, isGoodForDisplay) {
                    console.log("[CCornersInfo] 创建三参数构造函数实例");
                    console.log("  类型:", type.toString());
                    console.log("  点数组长度:", points ? points.length : 0);
                    console.log("  是否适合显示:", isGoodForDisplay);
                    
                    detectionData.totalCount++;
                    
                    // 打印角点坐标
                    if (points && points.length > 0) {
                        console.log("  角点坐标:");
                        for (let i = 0; i < points.length; i++) {
                            let point = points[i];
                            if (point) {
                                let x = point.x.value;
                                let y = point.y.value;
                                console.log(`    点${i + 1}: (${x.toFixed(4)}, ${y.toFixed(4)})`);
                            }
                        }
                        
                        // 存储数据
                        let cornerData = [];
                        for (let i = 0; i < points.length; i++) {
                            if (points[i]) {
                                cornerData.push({
                                    x: points[i].x.value,
                                    y: points[i].y.value
                                });
                            }
                        }
                        
                        detectionData.cornerResults.push({
                            timestamp: Date.now(),
                            type: type.toString(),
                            corners: cornerData,
                            cornerCount: points.length,
                            isGoodForDisplay: isGoodForDisplay,
                            constructorType: "three_param"
                        });
                        
                        console.log(`[数据] 已存储第${detectionData.cornerResults.length}个检测结果`);
                    }
                    
                    return this.$init(type, points, isGoodForDisplay);
                };
                console.log("[+] Hook三参数构造函数成功");
            } catch (e) {
                console.log("[!] Hook三参数构造函数失败:", e);
            }
            
            // Hook getPointsRef方法
            try {
                CCornersInfo.getPointsRef.implementation = function() {
                    let points = this.getPointsRef();
                    console.log("[CCornersInfo] getPointsRef调用，返回点数:", points ? points.length : 0);
                    
                    if (points && points.length > 0) {
                        console.log("  返回的角点:");
                        for (let i = 0; i < points.length; i++) {
                            if (points[i]) {
                                let x = points[i].x.value;
                                let y = points[i].y.value;
                                console.log(`    点${i + 1}: (${x.toFixed(4)}, ${y.toFixed(4)})`);
                            }
                        }
                    }
                    
                    return points;
                };
                console.log("[+] Hook getPointsRef方法成功");
            } catch (e) {
                console.log("[!] Hook getPointsRef方法失败:", e);
            }
            
            console.log("[+] CCornersInfo Hook 设置完成");
            
        } catch (e) {
            console.log("[!] Hook CCornersInfo 失败:", e);
            console.log("[!] 错误详情:", e.stack);
        }
    });
}

// 数据导出函数
function exportDetectionData() {
    console.log("\n[+] 导出检测数据...");
    console.log(`[统计] 总构造函数调用次数: ${detectionData.totalCount}`);
    console.log(`[统计] 有效角点数据: ${detectionData.cornerResults.length}`);
    
    if (detectionData.cornerResults.length > 0) {
        let dataStr = JSON.stringify(detectionData, null, 2);
        console.log("[数据导出]", dataStr);
        
        // 尝试写入文件
        try {
            let file = new File("/sdcard/ccornersinfo_test_data.json", "w");
            file.write(dataStr);
            file.close();
            console.log("[+] 数据已导出到 /sdcard/ccornersinfo_test_data.json");
        } catch (e) {
            console.log("[!] 文件写入失败:", e);
        }
    } else {
        console.log("[!] 没有检测到有效的角点数据");
    }
}

// 显示统计信息
function showStats() {
    console.log("\n=== 检测统计 ===");
    console.log(`总构造函数调用: ${detectionData.totalCount}`);
    console.log(`有效角点数据: ${detectionData.cornerResults.length}`);
    
    if (detectionData.cornerResults.length > 0) {
        console.log("\n最近的检测结果:");
        let recent = detectionData.cornerResults[detectionData.cornerResults.length - 1];
        console.log(`  类型: ${recent.type}`);
        console.log(`  角点数: ${recent.cornerCount}`);
        console.log(`  构造函数类型: ${recent.constructorType}`);
        if (recent.corners && recent.corners.length > 0) {
            console.log("  角点坐标:");
            recent.corners.forEach((corner, i) => {
                console.log(`    点${i + 1}: (${corner.x.toFixed(4)}, ${corner.y.toFixed(4)})`);
            });
        }
    }
}

// 主要Hook逻辑
function main() {
    console.log("[+] 开始CCornersInfo测试...");
    
    // 等待应用加载
    setTimeout(function() {
        hookCCornersInfo();
        
        console.log("[+] Hook设置完成");
        console.log("[+] 请在Adobe Scan中进行文档检测...");
        console.log("[+] 可用命令:");
        console.log("    showStats() - 显示统计信息");
        console.log("    exportData() - 导出数据");
        console.log("    clearData() - 清空数据");
        
        // 定期显示统计信息
        setInterval(function() {
            if (detectionData.totalCount > 0) {
                showStats();
            }
        }, 10000); // 每10秒显示一次
        
        // 定期导出数据
        setInterval(function() {
            if (detectionData.cornerResults.length > 0) {
                exportDetectionData();
            }
        }, 30000); // 每30秒导出一次
        
    }, 2000);
}

// 启动测试
main();

// 导出函数供手动调用
rpc.exports = {
    showStats: showStats,
    exportData: exportDetectionData,
    clearData: function() {
        detectionData = {
            cornerResults: [],
            totalCount: 0
        };
        console.log("[+] 检测数据已清空");
    },
    getData: function() {
        return detectionData;
    }
};

console.log("[+] CCornersInfo测试脚本加载完成，开始监控...");
