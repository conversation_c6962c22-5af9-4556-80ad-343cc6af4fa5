#!/bin/bash

# Adobe Scan 文档检测分析自动化脚本
# 用于对比Adobe原始实现和Python实现的差异

echo "=== Adobe Scan 文档检测分析 ==="
echo

# 检查必要的工具
check_requirements() {
    echo "[检查] 验证必要工具..."
    
    # 检查Python
    if ! command -v python3 &> /dev/null; then
        echo "[错误] Python3 未安装"
        exit 1
    fi
    
    # 检查Frida
    if ! command -v frida &> /dev/null; then
        echo "[警告] Frida 未安装，无法捕获Adobe数据"
        echo "请安装: pip install frida-tools"
    fi
    
    # 检查ADB
    if ! command -v adb &> /dev/null; then
        echo "[警告] ADB 未安装，无法连接Android设备"
    fi
    
    echo "[检查] 工具检查完成"
}

# 安装Python依赖
install_python_deps() {
    echo "[安装] 安装Python依赖..."
    
    pip3 install -r requirements.txt 2>/dev/null || {
        echo "[安装] 手动安装依赖..."
        pip3 install opencv-python numpy tensorflow matplotlib scipy scikit-learn
    }
    
    echo "[安装] Python依赖安装完成"
}

# 创建测试图像
create_test_image() {
    echo "[测试] 创建测试文档图像..."
    
    python3 document_edge_detector.py --create-test
    
    if [ -f "test_document.jpg" ]; then
        echo "[测试] 测试图像创建成功: test_document.jpg"
    else
        echo "[错误] 测试图像创建失败"
        exit 1
    fi
}

# 运行Python检测
run_python_detection() {
    echo "[Python] 运行Python文档检测..."
    
    # 检查模型文件
    MODEL_PATH="FindDocEdgeDemo/app/src/main/assets/models/capture_6_22_23.tflite"
    if [ ! -f "$MODEL_PATH" ]; then
        echo "[错误] 模型文件不存在: $MODEL_PATH"
        echo "请确保模型文件路径正确"
        exit 1
    fi
    
    # 运行检测
    python3 document_edge_detector.py \
        --image test_document.jpg \
        --model "$MODEL_PATH" \
        --output python_result.jpg \
        --show > python_detection.log 2>&1
    
    if [ $? -eq 0 ]; then
        echo "[Python] Python检测完成，结果保存到 python_result.jpg"
        echo "[Python] 日志保存到 python_detection.log"
    else
        echo "[错误] Python检测失败，查看日志: python_detection.log"
        cat python_detection.log
    fi
}

# 启动Frida分析（需要手动操作）
start_frida_analysis() {
    echo "[Frida] 准备启动Frida分析..."
    
    # 检查设备连接
    if command -v adb &> /dev/null; then
        DEVICES=$(adb devices | grep -v "List of devices" | grep "device$" | wc -l)
        if [ $DEVICES -eq 0 ]; then
            echo "[警告] 没有检测到Android设备"
            echo "请连接Android设备并启用USB调试"
        else
            echo "[Frida] 检测到 $DEVICES 个Android设备"
        fi
    fi
    
    echo "[Frida] 请手动执行以下命令来捕获Adobe数据:"
    echo "frida -U -f com.adobe.scan.android -l frida_adobe_scan_analysis.js"
    echo
    echo "[Frida] 然后在Adobe Scan中进行文档检测操作"
    echo "[Frida] 数据将保存到 /sdcard/adobe_scan_detection_data.json"
    echo
    echo "按Enter键继续（在完成Frida数据捕获后）..."
    read
}

# 获取Frida数据
get_frida_data() {
    echo "[Frida] 获取Frida捕获的数据..."
    
    if command -v adb &> /dev/null; then
        adb pull /sdcard/adobe_scan_detection_data.json . 2>/dev/null
        
        if [ -f "adobe_scan_detection_data.json" ]; then
            echo "[Frida] Adobe数据获取成功: adobe_scan_detection_data.json"
            return 0
        else
            echo "[警告] 未找到Adobe数据文件"
            echo "请确保Frida脚本已正确运行并捕获了数据"
            return 1
        fi
    else
        echo "[警告] ADB不可用，无法获取Frida数据"
        return 1
    fi
}

# 运行数据对比分析
run_comparison_analysis() {
    echo "[分析] 运行数据对比分析..."
    
    # 创建输出目录
    mkdir -p comparison_results
    
    # 运行分析
    if [ -f "adobe_scan_detection_data.json" ]; then
        python3 data_comparison_analyzer.py \
            --adobe-data adobe_scan_detection_data.json \
            --image test_document.jpg \
            --output-dir comparison_results
    else
        echo "[分析] 仅分析Python结果（没有Adobe数据）"
        python3 data_comparison_analyzer.py \
            --image test_document.jpg \
            --output-dir comparison_results
    fi
    
    echo "[分析] 分析完成，结果保存在 comparison_results/ 目录"
}

# 生成分析报告
generate_report() {
    echo "[报告] 生成分析报告..."
    
    REPORT_FILE="analysis_report.md"
    
    cat > "$REPORT_FILE" << EOF
# Adobe Scan 文档检测分析报告

## 分析概述

本报告对比了Adobe Scan原始实现和Python实现的文档边缘检测结果。

## 文件说明

- \`test_document.jpg\`: 测试文档图像
- \`python_result.jpg\`: Python检测结果
- \`python_detection.log\`: Python检测日志
- \`adobe_scan_detection_data.json\`: Frida捕获的Adobe数据
- \`comparison_results/\`: 对比分析结果

## Python检测结果

EOF
    
    if [ -f "python_detection.log" ]; then
        echo "### Python检测日志" >> "$REPORT_FILE"
        echo '```' >> "$REPORT_FILE"
        tail -20 python_detection.log >> "$REPORT_FILE"
        echo '```' >> "$REPORT_FILE"
        echo >> "$REPORT_FILE"
    fi
    
    if [ -f "comparison_results/comparison_report.txt" ]; then
        echo "### 对比分析结果" >> "$REPORT_FILE"
        echo '```' >> "$REPORT_FILE"
        cat comparison_results/comparison_report.txt >> "$REPORT_FILE"
        echo '```' >> "$REPORT_FILE"
    fi
    
    cat >> "$REPORT_FILE" << EOF

## 问题分析

如果Python实现生成的是绿色对角线，可能的原因包括：

1. **角点选择算法问题**: 从13个检测点中选择4个角点的策略不正确
2. **坐标系转换问题**: 归一化坐标到像素坐标的转换有误
3. **模型输出解析问题**: 对TensorFlow Lite模型输出的理解与Adobe实现不一致
4. **角点排序问题**: 选中的角点顺序不正确

## 建议解决方案

1. 使用Frida分析Adobe的实际数据流
2. 对比模型的原始输出数据
3. 改进角点选择算法
4. 验证坐标转换逻辑

## 下一步行动

1. 运行Frida脚本捕获Adobe数据
2. 分析Adobe的角点选择策略
3. 改进Python实现的算法
4. 进行更多测试验证
EOF
    
    echo "[报告] 分析报告已生成: $REPORT_FILE"
}

# 主流程
main() {
    echo "开始Adobe Scan文档检测分析..."
    echo
    
    # 检查环境
    check_requirements
    echo
    
    # 安装依赖
    install_python_deps
    echo
    
    # 创建测试图像
    create_test_image
    echo
    
    # 运行Python检测
    run_python_detection
    echo
    
    # 提示用户运行Frida
    echo "=== 第二阶段：Adobe数据捕获 ==="
    echo "现在需要使用Frida捕获Adobe Scan的实际数据"
    echo
    start_frida_analysis
    
    # 获取Frida数据
    get_frida_data
    echo
    
    # 运行对比分析
    run_comparison_analysis
    echo
    
    # 生成报告
    generate_report
    echo
    
    echo "=== 分析完成 ==="
    echo "请查看以下文件："
    echo "- analysis_report.md: 完整分析报告"
    echo "- comparison_results/: 对比分析结果"
    echo "- python_result.jpg: Python检测结果图像"
    echo
    echo "如果Python结果显示绿色对角线，请参考报告中的建议进行调试。"
}

# 运行主流程
main "$@"
