#!/usr/bin/env python3
"""
分析文档边缘检测结果
基于刚才运行的输出数据进行分析
"""

import numpy as np
import matplotlib.pyplot as plt
import cv2

def analyze_model_output():
    """分析模型输出数据"""
    print("=== 模型输出数据分析 ===")
    
    # 从刚才的运行结果中提取的13个点
    model_output = np.array([
        [8.64614826e-03, 9.91353810e-01],  # 点1: (0.0086, 0.9914) - 左下角
        [9.99759614e-01, 2.40401409e-04],  # 点2: (0.9998, 0.0002) - 右上角
        [9.86375391e-01, 1.36245890e-02],  # 点3: (0.9864, 0.0136) - 右上角
        [1.00000000e+00, 1.11332294e-08],  # 点4: (1.0000, 0.0000) - 右上角
        [3.19845974e-03, 9.96801496e-01],  # 点5: (0.0032, 0.9968) - 左下角
        [9.99988079e-01, 1.19746537e-05],  # 点6: (1.0000, 0.0000) - 右上角
        [2.08458095e-11, 1.00000000e+00],  # 点7: (0.0000, 1.0000) - 左下角
        [9.86592829e-01, 1.34071978e-02],  # 点8: (0.9866, 0.0134) - 右上角
        [9.99992132e-01, 7.90419017e-06],  # 点9: (1.0000, 0.0000) - 右上角
        [9.87859666e-01, 1.21403532e-02],  # 点10: (0.9879, 0.0121) - 右上角
        [9.84644711e-01, 1.53552154e-02],  # 点11: (0.9846, 0.0154) - 右上角
        [9.98456001e-01, 1.54395285e-03],  # 点12: (0.9985, 0.0015) - 右上角
        [9.99999881e-01, 7.99343525e-08]   # 点13: (1.0000, 0.0000) - 右上角
    ])
    
    print(f"模型输出了 {len(model_output)} 个点")
    print("\n点的分布分析:")
    
    # 分析点的分布
    x_coords = model_output[:, 0]
    y_coords = model_output[:, 1]
    
    print(f"X坐标范围: [{np.min(x_coords):.4f}, {np.max(x_coords):.4f}]")
    print(f"Y坐标范围: [{np.min(y_coords):.4f}, {np.max(y_coords):.4f}]")
    print(f"X坐标平均值: {np.mean(x_coords):.4f}")
    print(f"Y坐标平均值: {np.mean(y_coords):.4f}")
    
    # 分析点的聚集情况
    print("\n点的聚集分析:")
    
    # 统计各个象限的点数
    q1 = np.sum((x_coords > 0.5) & (y_coords < 0.5))  # 右上
    q2 = np.sum((x_coords < 0.5) & (y_coords < 0.5))  # 左上
    q3 = np.sum((x_coords < 0.5) & (y_coords > 0.5))  # 左下
    q4 = np.sum((x_coords > 0.5) & (y_coords > 0.5))  # 右下
    
    print(f"右上象限 (Q1): {q1} 个点")
    print(f"左上象限 (Q2): {q2} 个点")
    print(f"左下象限 (Q3): {q3} 个点")
    print(f"右下象限 (Q4): {q4} 个点")
    
    # 分析问题
    print("\n问题分析:")
    if q1 > 8:  # 如果右上象限有超过8个点
        print("❌ 问题确认: 大部分点集中在右上象限")
        print("   这解释了为什么会出现'绿色对角线'问题")
    
    # 真实的文档角点 (归一化坐标)
    true_corners = np.array([
        [0.125, 0.13333333],  # 左上
        [0.875, 0.1],         # 右上
        [0.9, 0.86666667],    # 右下
        [0.1, 0.9]            # 左下
    ])
    
    print(f"\n真实文档角点:")
    for i, (x, y) in enumerate(true_corners):
        print(f"  角点 {i+1}: ({x:.4f}, {y:.4f})")
    
    # 计算模型输出点与真实角点的最近距离
    print(f"\n模型输出点与真实角点的距离分析:")
    for i, true_corner in enumerate(true_corners):
        distances = np.sqrt(np.sum((model_output - true_corner) ** 2, axis=1))
        min_dist_idx = np.argmin(distances)
        min_dist = distances[min_dist_idx]
        closest_point = model_output[min_dist_idx]
        
        print(f"真实角点 {i+1} {true_corner} 最近的模型点:")
        print(f"  点 {min_dist_idx+1}: {closest_point} (距离: {min_dist:.4f})")
    
    return model_output, true_corners

def analyze_corner_selection_problem():
    """分析角点选择问题"""
    print("\n=== 角点选择问题分析 ===")
    
    # 从运行结果看，最终选择的4个角点是:
    selected_corners = np.array([
        [0.9846, 0.0154],  # 角点1
        [0.9985, 0.0015],  # 角点2
        [1.0000, 0.0000],  # 角点3
        [1.0000, 0.0000],  # 角点4 (重复)
    ])
    
    print("当前选择的4个角点:")
    for i, (x, y) in enumerate(selected_corners):
        print(f"  角点 {i+1}: ({x:.4f}, {y:.4f})")
    
    print("\n问题分析:")
    print("1. 所有选择的角点都在右上角区域")
    print("2. 角点3和角点4完全重复")
    print("3. 没有选择到左侧和下方的点")
    print("4. 这导致绘制的多边形退化为一条线或很小的区域")
    
    return selected_corners

def suggest_solutions():
    """提出解决方案"""
    print("\n=== 解决方案建议 ===")
    
    print("问题根源:")
    print("1. 模型输出的13个点分布不均匀，大部分集中在右上角")
    print("2. 角点选择算法没有正确处理这种不均匀分布")
    print("3. 可能存在坐标系理解错误或模型输入预处理问题")
    
    print("\n解决方案:")
    print("1. 检查图像预处理:")
    print("   - 验证输入图像的尺寸和格式")
    print("   - 检查归一化和缩放是否正确")
    print("   - 确认RGBA通道的添加是否合适")
    
    print("2. 改进角点选择算法:")
    print("   - 强制从不同象限选择点")
    print("   - 使用更智能的分布式选择策略")
    print("   - 添加点的有效性验证")
    
    print("3. 模型输出验证:")
    print("   - 对比Adobe Scan的实际输出")
    print("   - 检查模型是否正确加载")
    print("   - 验证推理参数设置")
    
    print("4. 坐标系验证:")
    print("   - 确认坐标系的原点位置")
    print("   - 检查X/Y轴的方向")
    print("   - 验证归一化坐标的含义")

def create_improved_corner_selection():
    """创建改进的角点选择算法"""
    print("\n=== 改进的角点选择算法 ===")
    
    # 模拟的13个点
    all_points = np.array([
        [8.64614826e-03, 9.91353810e-01],  # 左下
        [9.99759614e-01, 2.40401409e-04],  # 右上
        [9.86375391e-01, 1.36245890e-02],  # 右上
        [1.00000000e+00, 1.11332294e-08],  # 右上
        [3.19845974e-03, 9.96801496e-01],  # 左下
        [9.99988079e-01, 1.19746537e-05],  # 右上
        [2.08458095e-11, 1.00000000e+00],  # 左下
        [9.86592829e-01, 1.34071978e-02],  # 右上
        [9.99992132e-01, 7.90419017e-06],  # 右上
        [9.87859666e-01, 1.21403532e-02],  # 右上
        [9.84644711e-01, 1.53552154e-02],  # 右上
        [9.98456001e-01, 1.54395285e-03],  # 右上
        [9.99999881e-01, 7.99343525e-08]   # 右上
    ])
    
    print("强制象限分布选择:")
    
    # 强制从每个象限选择一个点
    def select_from_quadrant(points, x_thresh=0.5, y_thresh=0.5, target_x='high', target_y='low'):
        if target_x == 'high' and target_y == 'low':  # 右上
            mask = (points[:, 0] > x_thresh) & (points[:, 1] < y_thresh)
        elif target_x == 'low' and target_y == 'low':  # 左上
            mask = (points[:, 0] < x_thresh) & (points[:, 1] < y_thresh)
        elif target_x == 'low' and target_y == 'high':  # 左下
            mask = (points[:, 0] < x_thresh) & (points[:, 1] > y_thresh)
        else:  # 右下
            mask = (points[:, 0] > x_thresh) & (points[:, 1] > y_thresh)
        
        if np.any(mask):
            candidates = points[mask]
            # 选择最极端的点
            if target_x == 'high' and target_y == 'low':
                idx = np.argmax(candidates[:, 0] - candidates[:, 1])
            elif target_x == 'low' and target_y == 'low':
                idx = np.argmin(candidates[:, 0] + candidates[:, 1])
            elif target_x == 'low' and target_y == 'high':
                idx = np.argmin(candidates[:, 0] - candidates[:, 1])
            else:
                idx = np.argmax(candidates[:, 0] + candidates[:, 1])
            return candidates[idx]
        return None
    
    # 尝试从每个象限选择点
    top_right = select_from_quadrant(all_points, target_x='high', target_y='low')
    top_left = select_from_quadrant(all_points, target_x='low', target_y='low')
    bottom_left = select_from_quadrant(all_points, target_x='low', target_y='high')
    bottom_right = select_from_quadrant(all_points, target_x='high', target_y='high')
    
    improved_corners = []
    if top_left is not None:
        improved_corners.append(top_left)
        print(f"左上角点: ({top_left[0]:.4f}, {top_left[1]:.4f})")
    if top_right is not None:
        improved_corners.append(top_right)
        print(f"右上角点: ({top_right[0]:.4f}, {top_right[1]:.4f})")
    if bottom_right is not None:
        improved_corners.append(bottom_right)
        print(f"右下角点: ({bottom_right[0]:.4f}, {bottom_right[1]:.4f})")
    if bottom_left is not None:
        improved_corners.append(bottom_left)
        print(f"左下角点: ({bottom_left[0]:.4f}, {bottom_left[1]:.4f})")
    
    # 如果某些象限没有点，使用备选策略
    if len(improved_corners) < 4:
        print(f"\n只找到 {len(improved_corners)} 个象限的点，使用备选策略...")
        
        # 选择最分散的4个点
        remaining_points = all_points.copy()
        selected_indices = []
        
        # 贪心选择最分散的点
        for i in range(4):
            if i == 0:
                # 第一个点选择最左上的
                distances_to_origin = np.sqrt(remaining_points[:, 0]**2 + remaining_points[:, 1]**2)
                best_idx = np.argmin(distances_to_origin)
            else:
                # 后续点选择与已选点距离最大的
                max_min_dist = -1
                best_idx = -1
                for j, point in enumerate(remaining_points):
                    if j in selected_indices:
                        continue
                    min_dist = float('inf')
                    for selected_idx in selected_indices:
                        dist = np.sqrt(np.sum((point - remaining_points[selected_idx])**2))
                        min_dist = min(min_dist, dist)
                    if min_dist > max_min_dist:
                        max_min_dist = min_dist
                        best_idx = j
            
            if best_idx != -1:
                selected_indices.append(best_idx)
                print(f"备选角点 {i+1}: ({remaining_points[best_idx][0]:.4f}, {remaining_points[best_idx][1]:.4f})")
    
    return improved_corners

def main():
    """主函数"""
    print("Adobe Scan 文档边缘检测结果分析")
    print("=" * 50)
    
    # 分析模型输出
    model_output, true_corners = analyze_model_output()
    
    # 分析角点选择问题
    selected_corners = analyze_corner_selection_problem()
    
    # 提出解决方案
    suggest_solutions()
    
    # 创建改进的角点选择
    improved_corners = create_improved_corner_selection()
    
    print("\n" + "=" * 50)
    print("分析完成！")
    print("\n下一步建议:")
    print("1. 修改 document_edge_detector.py 中的角点选择算法")
    print("2. 添加强制象限分布的选择策略")
    print("3. 验证图像预处理流程")
    print("4. 使用Frida对比Adobe Scan的实际输出")

if __name__ == "__main__":
    main()
