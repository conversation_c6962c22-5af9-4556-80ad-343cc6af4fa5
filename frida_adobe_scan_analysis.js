/**
 * Frida脚本：分析Adobe Scan的文档边缘检测数据
 * 用于对比Python实现与Adobe原始实现的差异
 */

console.log("[+] Adobe Scan 文档检测数据分析脚本启动");

// 全局变量存储检测数据
let detectionData = {
    modelInputs: [],
    modelOutputs: [],
    cornerResults: [],
    imageInfo: []
};

// Hook TensorFlow Lite 推理
function hookTensorFlowLite() {
    console.log("[+] 开始Hook TensorFlow Lite...");
    
    // Hook TfLiteInterpreter::Invoke
    let tfliteLib = Process.findModuleByName("libtensorflowlite.so");
    if (tfliteLib) {
        console.log("[+] 找到 TensorFlow Lite 库");
        
        // 查找 Invoke 函数
        let invokeSymbol = tfliteLib.enumerateSymbols().find(s => 
            s.name.includes("Invoke") && s.name.includes("Interpreter"));
        
        if (invokeSymbol) {
            console.log("[+] Hook TfLiteInterpreter::Invoke");
            Interceptor.attach(invokeSymbol.address, {
                onEnter: function(args) {
                    console.log("[TFLite] 模型推理开始");
                    this.interpreter = args[0];
                },
                onLeave: function(retval) {
                    console.log("[TFLite] 模型推理完成，返回值:", retval);
                    
                    // 尝试读取输出数据
                    try {
                        this.captureModelOutput();
                    } catch (e) {
                        console.log("[!] 捕获模型输出失败:", e);
                    }
                },
                captureModelOutput: function() {
                    // 这里需要根据具体的TFLite API来读取输出
                    console.log("[TFLite] 尝试捕获模型输出数据...");
                }
            });
        }
    }
}

// Hook CCornersInfo 相关函数
function hookCCornersInfo() {
    console.log("[+] 开始Hook CCornersInfo...");
    
    // Hook CCornersInfo 构造函数
    Java.perform(function() {
        try {
            let CCornersInfo = Java.use("com.adobe.magic_clean.CCornersInfo");
            
            // Hook 构造函数
            CCornersInfo.$init.overload('[F', 'int').implementation = function(corners, type) {
                console.log("[CCornersInfo] 创建新的角点信息");
                console.log("  类型:", type);
                console.log("  角点数据长度:", corners.length);
                
                // 打印角点坐标
                if (corners && corners.length >= 8) {
                    console.log("  角点坐标:");
                    for (let i = 0; i < corners.length; i += 2) {
                        let x = corners[i];
                        let y = corners[i + 1];
                        console.log(`    点${i/2 + 1}: (${x.toFixed(4)}, ${y.toFixed(4)})`);
                    }
                    
                    // 存储数据
                    detectionData.cornerResults.push({
                        timestamp: Date.now(),
                        type: type,
                        corners: Array.from(corners),
                        cornerCount: corners.length / 2
                    });
                }
                
                return this.$init(corners, type);
            };
            
            console.log("[+] CCornersInfo Hook 设置完成");
            
        } catch (e) {
            console.log("[!] Hook CCornersInfo 失败:", e);
        }
    });
}

// Hook 图像处理相关函数
function hookImageProcessing() {
    console.log("[+] 开始Hook图像处理...");
    
    Java.perform(function() {
        try {
            // Hook 可能的图像预处理函数
            let classes = Java.enumerateLoadedClassesSync();
            let scanClasses = classes.filter(name => 
                name.includes("adobe") && name.includes("scan"));
            
            console.log("[+] 找到Adobe Scan相关类:", scanClasses.length);
            
            // 尝试Hook一些常见的图像处理方法
            scanClasses.forEach(className => {
                try {
                    let clazz = Java.use(className);
                    let methods = clazz.class.getDeclaredMethods();
                    
                    methods.forEach(method => {
                        let methodName = method.getName();
                        if (methodName.includes("process") || 
                            methodName.includes("detect") ||
                            methodName.includes("corner") ||
                            methodName.includes("edge")) {
                            
                            console.log(`[+] 发现可能的检测方法: ${className}.${methodName}`);
                        }
                    });
                } catch (e) {
                    // 忽略无法访问的类
                }
            });
            
        } catch (e) {
            console.log("[!] Hook图像处理失败:", e);
        }
    });
}

// Hook Native层的文档检测函数
function hookNativeDetection() {
    console.log("[+] 开始Hook Native层检测函数...");
    
    // 查找可能的native库
    let nativeLibs = Process.enumerateModules().filter(m => 
        m.name.includes("scan") || m.name.includes("adobe"));
    
    console.log("[+] 找到可能的native库:", nativeLibs.map(m => m.name));
    
    nativeLibs.forEach(lib => {
        try {
            // 查找可能的检测函数
            let symbols = lib.enumerateSymbols().filter(s => 
                s.name.includes("detect") || 
                s.name.includes("corner") ||
                s.name.includes("edge") ||
                s.name.includes("document"));
            
            if (symbols.length > 0) {
                console.log(`[+] 在${lib.name}中找到可能的检测函数:`, 
                    symbols.map(s => s.name));
                
                // Hook这些函数
                symbols.forEach(symbol => {
                    try {
                        Interceptor.attach(symbol.address, {
                            onEnter: function(args) {
                                console.log(`[Native] 调用 ${symbol.name}`);
                                // 记录参数
                                for (let i = 0; i < Math.min(args.length, 4); i++) {
                                    console.log(`  arg[${i}]:`, args[i]);
                                }
                            },
                            onLeave: function(retval) {
                                console.log(`[Native] ${symbol.name} 返回:`, retval);
                            }
                        });
                    } catch (e) {
                        console.log(`[!] Hook ${symbol.name} 失败:`, e);
                    }
                });
            }
        } catch (e) {
            console.log(`[!] 分析库 ${lib.name} 失败:`, e);
        }
    });
}

// 数据导出函数
function exportDetectionData() {
    console.log("[+] 导出检测数据...");
    
    let dataStr = JSON.stringify(detectionData, null, 2);
    console.log("[数据导出]", dataStr);
    
    // 也可以写入文件
    let file = new File("/sdcard/adobe_scan_detection_data.json", "w");
    file.write(dataStr);
    file.close();
    
    console.log("[+] 数据已导出到 /sdcard/adobe_scan_detection_data.json");
}

// 主要Hook逻辑
function main() {
    console.log("[+] 开始分析Adobe Scan文档检测...");
    
    // 等待应用加载
    setTimeout(function() {
        hookCCornersInfo();
        hookImageProcessing();
        hookNativeDetection();
        hookTensorFlowLite();
        
        console.log("[+] 所有Hook设置完成");
        console.log("[+] 请在Adobe Scan中进行文档检测...");
        
        // 定期导出数据
        setInterval(function() {
            if (detectionData.cornerResults.length > 0) {
                exportDetectionData();
            }
        }, 10000); // 每10秒导出一次
        
    }, 2000);
}

// 启动分析
main();

// 手动导出数据的函数
rpc.exports = {
    exportData: exportDetectionData,
    getDetectionData: function() {
        return detectionData;
    },
    clearData: function() {
        detectionData = {
            modelInputs: [],
            modelOutputs: [],
            cornerResults: [],
            imageInfo: []
        };
        console.log("[+] 检测数据已清空");
    }
};

console.log("[+] Frida脚本加载完成，开始监控Adobe Scan...");
