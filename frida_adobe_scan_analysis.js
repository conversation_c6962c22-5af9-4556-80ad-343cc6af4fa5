/**
 * Frida脚本：分析Adobe Scan的文档边缘检测数据
 * 用于对比Python实现与Adobe原始实现的差异
 */

console.log("[+] Adobe Scan 文档检测数据分析脚本启动");

// 全局变量存储检测数据
let detectionData = {
    modelInputs: [],
    modelOutputs: [],
    cornerResults: [],
    imageInfo: []
};

// Hook TensorFlow Lite 推理
function hookTensorFlowLite() {
    console.log("[+] 开始Hook TensorFlow Lite...");
    
    // Hook TfLiteInterpreter::Invoke
    let tfliteLib = Process.findModuleByName("libtensorflowlite.so");
    if (tfliteLib) {
        console.log("[+] 找到 TensorFlow Lite 库");
        
        // 查找 Invoke 函数
        let invokeSymbol = tfliteLib.enumerateSymbols().find(s => 
            s.name.includes("Invoke") && s.name.includes("Interpreter"));
        
        if (invokeSymbol) {
            console.log("[+] Hook TfLiteInterpreter::Invoke");
            Interceptor.attach(invokeSymbol.address, {
                onEnter: function(args) {
                    console.log("[TFLite] 模型推理开始");
                    this.interpreter = args[0];
                },
                onLeave: function(retval) {
                    console.log("[TFLite] 模型推理完成，返回值:", retval);
                    
                    // 尝试读取输出数据
                    try {
                        this.captureModelOutput();
                    } catch (e) {
                        console.log("[!] 捕获模型输出失败:", e);
                    }
                },
                captureModelOutput: function() {
                    // 这里需要根据具体的TFLite API来读取输出
                    console.log("[TFLite] 尝试捕获模型输出数据...");
                }
            });
        }
    }
}

// Hook CCornersInfo 相关函数
function hookCCornersInfo() {
    console.log("[+] 开始Hook CCornersInfo...");

    // Hook CCornersInfo 构造函数
    Java.perform(function() {
        try {
            let CCornersInfo = Java.use("com.adobe.magic_clean.CCornersInfo");
            let CCornersInfoType = Java.use("com.adobe.magic_clean.CCornersInfo$CCornersInfoType");

            console.log("[+] 成功获取CCornersInfo类");

            // Hook 无参构造函数
            try {
                CCornersInfo.$init.overload().implementation = function() {
                    console.log("[CCornersInfo] 创建无参构造函数实例");
                    return this.$init();
                };
                console.log("[+] Hook无参构造函数成功");
            } catch (e) {
                console.log("[!] Hook无参构造函数失败:", e);
            }

            // Hook 两参数构造函数 (CCornersInfoType, PointF[])
            try {
                CCornersInfo.$init.overload('com.adobe.magic_clean.CCornersInfo$CCornersInfoType', '[Landroid.graphics.PointF;').implementation = function(type, points) {
                    console.log("[CCornersInfo] 创建两参数构造函数实例");
                    console.log("  类型:", type.toString());
                    console.log("  点数组长度:", points ? points.length : 0);

                    // 打印角点坐标
                    if (points && points.length > 0) {
                        console.log("  角点坐标:");
                        for (let i = 0; i < points.length; i++) {
                            let point = points[i];
                            if (point) {
                                let x = point.x.value;
                                let y = point.y.value;
                                console.log(`    点${i + 1}: (${x.toFixed(4)}, ${y.toFixed(4)})`);
                            }
                        }

                        // 存储数据
                        let cornerData = [];
                        for (let i = 0; i < points.length; i++) {
                            if (points[i]) {
                                cornerData.push({
                                    x: points[i].x.value,
                                    y: points[i].y.value
                                });
                            }
                        }

                        detectionData.cornerResults.push({
                            timestamp: Date.now(),
                            type: type.toString(),
                            corners: cornerData,
                            cornerCount: points.length,
                            constructorType: "two_param"
                        });
                    }

                    return this.$init(type, points);
                };
                console.log("[+] Hook两参数构造函数成功");
            } catch (e) {
                console.log("[!] Hook两参数构造函数失败:", e);
            }

            // Hook 三参数构造函数 (CCornersInfoType, PointF[], boolean)
            try {
                CCornersInfo.$init.overload('com.adobe.magic_clean.CCornersInfo$CCornersInfoType', '[Landroid.graphics.PointF;', 'boolean').implementation = function(type, points, isGoodForDisplay) {
                    console.log("[CCornersInfo] 创建三参数构造函数实例");
                    console.log("  类型:", type.toString());
                    console.log("  点数组长度:", points ? points.length : 0);
                    console.log("  是否适合显示:", isGoodForDisplay);

                    // 打印角点坐标
                    if (points && points.length > 0) {
                        console.log("  角点坐标:");
                        for (let i = 0; i < points.length; i++) {
                            let point = points[i];
                            if (point) {
                                let x = point.x.value;
                                let y = point.y.value;
                                console.log(`    点${i + 1}: (${x.toFixed(4)}, ${y.toFixed(4)})`);
                            }
                        }

                        // 存储数据
                        let cornerData = [];
                        for (let i = 0; i < points.length; i++) {
                            if (points[i]) {
                                cornerData.push({
                                    x: points[i].x.value,
                                    y: points[i].y.value
                                });
                            }
                        }

                        detectionData.cornerResults.push({
                            timestamp: Date.now(),
                            type: type.toString(),
                            corners: cornerData,
                            cornerCount: points.length,
                            isGoodForDisplay: isGoodForDisplay,
                            constructorType: "three_param"
                        });
                    }

                    return this.$init(type, points, isGoodForDisplay);
                };
                console.log("[+] Hook三参数构造函数成功");
            } catch (e) {
                console.log("[!] Hook三参数构造函数失败:", e);
            }

            // Hook getPointsRef方法
            try {
                CCornersInfo.getPointsRef.implementation = function() {
                    let points = this.getPointsRef();
                    console.log("[CCornersInfo] getPointsRef调用，返回点数:", points ? points.length : 0);

                    if (points && points.length > 0) {
                        console.log("  返回的角点:");
                        for (let i = 0; i < points.length; i++) {
                            if (points[i]) {
                                let x = points[i].x.value;
                                let y = points[i].y.value;
                                console.log(`    点${i + 1}: (${x.toFixed(4)}, ${y.toFixed(4)})`);
                            }
                        }
                    }

                    return points;
                };
                console.log("[+] Hook getPointsRef方法成功");
            } catch (e) {
                console.log("[!] Hook getPointsRef方法失败:", e);
            }

            // Hook getClonedPoints方法
            try {
                CCornersInfo.getClonedPoints.implementation = function() {
                    let points = this.getClonedPoints();
                    console.log("[CCornersInfo] getClonedPoints调用，返回点数:", points ? points.length : 0);
                    return points;
                };
                console.log("[+] Hook getClonedPoints方法成功");
            } catch (e) {
                console.log("[!] Hook getClonedPoints方法失败:", e);
            }

            console.log("[+] CCornersInfo Hook 设置完成");

        } catch (e) {
            console.log("[!] Hook CCornersInfo 失败:", e);
            console.log("[!] 错误详情:", e.stack);
        }
    });
}

// Hook 图像处理相关函数
function hookImageProcessing() {
    console.log("[+] 开始Hook图像处理...");

    Java.perform(function() {
        try {
            // Hook CameraCleanLiveEdgeDetectionAndroidShim
            hookCameraCleanLiveEdgeDetection();

            // Hook CameraCleanAndroidShim
            hookCameraCleanAndroidShim();

            // Hook 其他边缘检测相关类
            hookOtherEdgeDetectionClasses();

        } catch (e) {
            console.log("[!] Hook图像处理失败:", e);
        }
    });
}

// Hook CameraCleanLiveEdgeDetectionAndroidShim类
function hookCameraCleanLiveEdgeDetection() {
    try {
        let CameraCleanLiveEdgeDetection = Java.use("com.adobe.magic_clean.CameraCleanLiveEdgeDetectionAndroidShim");
        console.log("[+] 成功获取CameraCleanLiveEdgeDetectionAndroidShim类");

        // Hook构造函数
        try {
            CameraCleanLiveEdgeDetection.$init.overload('double').implementation = function(threshold) {
                console.log("[CameraCleanLiveEdgeDetection] 创建实例，阈值:", threshold);
                return this.$init(threshold);
            };
        } catch (e) {
            console.log("[!] Hook CameraCleanLiveEdgeDetection构造函数失败:", e);
        }

        // Hook GetLiveCorners方法
        try {
            CameraCleanLiveEdgeDetection.GetLiveCorners.implementation = function(input, output) {
                console.log("[CameraCleanLiveEdgeDetection] GetLiveCorners调用");

                let result = this.GetLiveCorners(input, output);

                // 检查输出结果
                if (output && output.mCornersInfo) {
                    let cornersInfo = output.mCornersInfo;
                    let points = cornersInfo.getPointsRef();

                    console.log("  输出角点信息:");
                    console.log("    类型:", cornersInfo.mCornersType.toString());
                    console.log("    点数:", points ? points.length : 0);
                    console.log("    边界置信度:", output.mBoundaryConfidence);
                    console.log("    边界提示:", output.mLiveBoundaryHint.toString());

                    if (points && points.length > 0) {
                        console.log("    角点坐标:");
                        for (let i = 0; i < points.length; i++) {
                            if (points[i]) {
                                let x = points[i].x;
                                let y = points[i].y;
                                console.log(`      点${i + 1}: (${x.toFixed(4)}, ${y.toFixed(4)})`);
                            }
                        }

                        // 存储数据
                        let cornerData = [];
                        for (let i = 0; i < points.length; i++) {
                            if (points[i]) {
                                cornerData.push({
                                    x: points[i].x,
                                    y: points[i].y
                                });
                            }
                        }

                        detectionData.cornerResults.push({
                            timestamp: Date.now(),
                            source: "GetLiveCorners",
                            type: cornersInfo.mCornersType.toString(),
                            corners: cornerData,
                            cornerCount: points.length,
                            boundaryConfidence: output.mBoundaryConfidence,
                            boundaryHint: output.mLiveBoundaryHint.toString(),
                            isGoodForDisplay: cornersInfo.mIsGoodForDisplay
                        });
                    }
                }

                return result;
            };
            console.log("[+] Hook GetLiveCorners方法成功");
        } catch (e) {
            console.log("[!] Hook GetLiveCorners方法失败:", e);
        }

    } catch (e) {
        console.log("[!] Hook CameraCleanLiveEdgeDetection失败:", e);
    }
}

// Hook CameraCleanAndroidShim类
function hookCameraCleanAndroidShim() {
    try {
        let CameraCleanAndroidShim = Java.use("com.adobe.magic_clean.CameraCleanAndroidShim");
        console.log("[+] 成功获取CameraCleanAndroidShim类");

        // Hook GetCorners方法
        try {
            CameraCleanAndroidShim.GetCorners.implementation = function(input, output) {
                console.log("[CameraCleanAndroidShim] GetCorners调用");

                let result = this.GetCorners(input, output);

                // 检查输出结果
                if (output && output.mCorners && output.mCorners.length > 0) {
                    let corners = output.mCorners;
                    console.log("  检测到角点数:", corners.length);
                    console.log("  相机结果:", result.toString());

                    console.log("  角点坐标:");
                    let cornerData = [];
                    for (let i = 0; i < corners.length; i++) {
                        if (corners[i]) {
                            let x = corners[i].x;
                            let y = corners[i].y;
                            console.log(`    点${i + 1}: (${x.toFixed(4)}, ${y.toFixed(4)})`);
                            cornerData.push({ x: x, y: y });
                        }
                    }

                    // 存储数据
                    detectionData.cornerResults.push({
                        timestamp: Date.now(),
                        source: "GetCorners",
                        corners: cornerData,
                        cornerCount: corners.length,
                        cameraResult: result.toString()
                    });
                }

                return result;
            };
            console.log("[+] Hook GetCorners方法成功");
        } catch (e) {
            console.log("[!] Hook GetCorners方法失败:", e);
        }

    } catch (e) {
        console.log("[!] Hook CameraCleanAndroidShim失败:", e);
    }
}

// Hook其他边缘检测相关类
function hookOtherEdgeDetectionClasses() {
    // Hook EdgeDetectionInput和EdgeDetectionOutput
    try {
        let EdgeDetectionInput = Java.use("com.adobe.magic_clean.CameraCleanUtils$EdgeDetectionInput");
        let EdgeDetectionOutput = Java.use("com.adobe.magic_clean.CameraCleanUtils$EdgeDetectionOutput");

        console.log("[+] 成功获取EdgeDetection相关类");

        // 可以在这里添加更多的Hook

    } catch (e) {
        console.log("[!] Hook EdgeDetection相关类失败:", e);
    }

    // 搜索其他可能的边缘检测类
    try {
        let classes = Java.enumerateLoadedClassesSync();
        let edgeClasses = classes.filter(name =>
            (name.includes("adobe") && (
                name.includes("edge") ||
                name.includes("corner") ||
                name.includes("detect") ||
                name.includes("boundary")
            )) ||
            name.includes("magic_clean")
        );

        console.log("[+] 找到边缘检测相关类:", edgeClasses.length);
        edgeClasses.forEach(className => {
            console.log("  -", className);
        });

    } catch (e) {
        console.log("[!] 搜索边缘检测类失败:", e);
    }
}

// Hook Native层的文档检测函数
function hookNativeDetection() {
    console.log("[+] 开始Hook Native层检测函数...");
    
    // 查找可能的native库
    let nativeLibs = Process.enumerateModules().filter(m => 
        m.name.includes("scan") || m.name.includes("adobe"));
    
    console.log("[+] 找到可能的native库:", nativeLibs.map(m => m.name));
    
    nativeLibs.forEach(lib => {
        try {
            // 查找可能的检测函数
            let symbols = lib.enumerateSymbols().filter(s => 
                s.name.includes("detect") || 
                s.name.includes("corner") ||
                s.name.includes("edge") ||
                s.name.includes("document"));
            
            if (symbols.length > 0) {
                console.log(`[+] 在${lib.name}中找到可能的检测函数:`, 
                    symbols.map(s => s.name));
                
                // Hook这些函数
                symbols.forEach(symbol => {
                    try {
                        Interceptor.attach(symbol.address, {
                            onEnter: function(args) {
                                console.log(`[Native] 调用 ${symbol.name}`);
                                // 记录参数
                                for (let i = 0; i < Math.min(args.length, 4); i++) {
                                    console.log(`  arg[${i}]:`, args[i]);
                                }
                            },
                            onLeave: function(retval) {
                                console.log(`[Native] ${symbol.name} 返回:`, retval);
                            }
                        });
                    } catch (e) {
                        console.log(`[!] Hook ${symbol.name} 失败:`, e);
                    }
                });
            }
        } catch (e) {
            console.log(`[!] 分析库 ${lib.name} 失败:`, e);
        }
    });
}

// 数据导出函数
function exportDetectionData() {
    console.log("[+] 导出检测数据...");
    
    let dataStr = JSON.stringify(detectionData, null, 2);
    console.log("[数据导出]", dataStr);
    
    // 也可以写入文件
    let file = new File("/sdcard/adobe_scan_detection_data.json", "w");
    file.write(dataStr);
    file.close();
    
    console.log("[+] 数据已导出到 /sdcard/adobe_scan_detection_data.json");
}

// 主要Hook逻辑
function main() {
    console.log("[+] 开始分析Adobe Scan文档检测...");
    
    // 等待应用加载
    setTimeout(function() {
        hookCCornersInfo();
        hookImageProcessing();
        hookNativeDetection();
        hookTensorFlowLite();
        
        console.log("[+] 所有Hook设置完成");
        console.log("[+] 请在Adobe Scan中进行文档检测...");
        
        // 定期导出数据
        setInterval(function() {
            if (detectionData.cornerResults.length > 0) {
                exportDetectionData();
            }
        }, 10000); // 每10秒导出一次
        
    }, 2000);
}

// 启动分析
main();

// 手动导出数据的函数
rpc.exports = {
    exportData: exportDetectionData,
    getDetectionData: function() {
        return detectionData;
    },
    clearData: function() {
        detectionData = {
            modelInputs: [],
            modelOutputs: [],
            cornerResults: [],
            imageInfo: []
        };
        console.log("[+] 检测数据已清空");
    }
};

console.log("[+] Frida脚本加载完成，开始监控Adobe Scan...");
