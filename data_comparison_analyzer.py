#!/usr/bin/env python3
"""
Adobe Scan vs Python实现数据对比分析器
用于分析Frida捕获的Adobe数据和Python脚本输出的差异
"""

import json
import numpy as np
import matplotlib.pyplot as plt
import cv2
import os
from typing import Dict, List, Tuple, Optional
import argparse


class DataComparisonAnalyzer:
    """数据对比分析器"""
    
    def __init__(self):
        self.adobe_data = None
        self.python_data = None
        
    def load_adobe_data(self, frida_json_path: str):
        """加载Frida捕获的Adobe数据"""
        try:
            with open(frida_json_path, 'r', encoding='utf-8') as f:
                self.adobe_data = json.load(f)
            print(f"[Adobe数据] 加载成功: {frida_json_path}")
            print(f"  角点结果数量: {len(self.adobe_data.get('cornerResults', []))}")
            return True
        except Exception as e:
            print(f"[错误] 加载Adobe数据失败: {e}")
            return False
    
    def load_python_data(self, python_log_path: str):
        """加载Python脚本的输出数据"""
        try:
            # 这里可以解析Python脚本的日志输出
            # 或者修改Python脚本来输出JSON格式的数据
            print(f"[Python数据] 加载: {python_log_path}")
            # TODO: 实现Python数据解析
            return True
        except Exception as e:
            print(f"[错误] 加载Python数据失败: {e}")
            return False
    
    def analyze_corner_differences(self):
        """分析角点检测的差异"""
        if not self.adobe_data:
            print("[错误] 没有Adobe数据")
            return
        
        print("\n=== Adobe Scan 角点数据分析 ===")
        
        corner_results = self.adobe_data.get('cornerResults', [])
        
        for i, result in enumerate(corner_results):
            print(f"\n[结果 {i+1}]")
            print(f"  时间戳: {result.get('timestamp')}")
            print(f"  类型: {result.get('type')}")
            print(f"  角点数量: {result.get('cornerCount')}")
            
            corners = result.get('corners', [])
            if corners and len(corners) >= 8:
                print("  角点坐标:")
                for j in range(0, len(corners), 2):
                    if j + 1 < len(corners):
                        x, y = corners[j], corners[j + 1]
                        print(f"    点{j//2 + 1}: ({x:.4f}, {y:.4f})")
                
                # 分析角点分布
                self._analyze_corner_distribution(corners)
    
    def _analyze_corner_distribution(self, corners: List[float]):
        """分析角点分布特征"""
        if len(corners) < 8:
            return
        
        # 转换为点数组
        points = []
        for i in range(0, len(corners), 2):
            if i + 1 < len(corners):
                points.append([corners[i], corners[i + 1]])
        
        points = np.array(points)
        
        print(f"    分布分析:")
        print(f"      X范围: [{np.min(points[:, 0]):.4f}, {np.max(points[:, 0]):.4f}]")
        print(f"      Y范围: [{np.min(points[:, 1]):.4f}, {np.max(points[:, 1]):.4f}]")
        print(f"      中心点: ({np.mean(points[:, 0]):.4f}, {np.mean(points[:, 1]):.4f})")
        
        # 计算点之间的距离
        if len(points) >= 4:
            distances = []
            for i in range(len(points)):
                for j in range(i + 1, len(points)):
                    dist = np.sqrt(np.sum((points[i] - points[j]) ** 2))
                    distances.append(dist)
            
            print(f"      平均距离: {np.mean(distances):.4f}")
            print(f"      距离标准差: {np.std(distances):.4f}")
    
    def visualize_comparison(self, image_path: str, output_dir: str = "comparison_results"):
        """可视化对比结果"""
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        # 加载图像
        image = cv2.imread(image_path)
        if image is None:
            print(f"[错误] 无法加载图像: {image_path}")
            return
        
        h, w = image.shape[:2]
        
        # 为每个Adobe结果创建可视化
        corner_results = self.adobe_data.get('cornerResults', [])
        
        for i, result in enumerate(corner_results):
            corners = result.get('corners', [])
            if len(corners) < 8:
                continue
            
            # 创建可视化图像
            vis_image = image.copy()
            
            # 绘制所有检测点
            for j in range(0, len(corners), 2):
                if j + 1 < len(corners):
                    x = int(corners[j] * w)
                    y = int(corners[j + 1] * h)
                    
                    # 绘制点
                    cv2.circle(vis_image, (x, y), 5, (0, 255, 0), -1)
                    cv2.putText(vis_image, f"{j//2 + 1}", (x + 8, y - 8),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
            
            # 如果有4个或更多点，尝试连接形成多边形
            if len(corners) >= 8:
                # 选择前4个点或使用某种策略选择4个角点
                selected_corners = self._select_4_corners_from_adobe_data(corners)
                
                if selected_corners and len(selected_corners) == 4:
                    # 转换为像素坐标
                    pixel_corners = []
                    for corner in selected_corners:
                        x = int(corner[0] * w)
                        y = int(corner[1] * h)
                        pixel_corners.append([x, y])
                    
                    # 绘制多边形
                    pts = np.array(pixel_corners, dtype=np.int32)
                    cv2.polylines(vis_image, [pts], True, (0, 0, 255), 2)
                    
                    # 标记选中的角点
                    for k, (x, y) in enumerate(pixel_corners):
                        cv2.circle(vis_image, (x, y), 8, (0, 0, 255), 2)
                        cv2.putText(vis_image, f"C{k+1}", (x + 10, y + 10),
                                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
            
            # 添加信息文本
            info_text = f"Adobe Result {i+1} - Type: {result.get('type')} - Points: {len(corners)//2}"
            cv2.putText(vis_image, info_text, (10, 30),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            
            # 保存结果
            output_path = os.path.join(output_dir, f"adobe_result_{i+1}.jpg")
            cv2.imwrite(output_path, vis_image)
            print(f"[可视化] 保存Adobe结果 {i+1}: {output_path}")
    
    def _select_4_corners_from_adobe_data(self, corners: List[float]) -> Optional[List[Tuple[float, float]]]:
        """从Adobe数据中选择4个角点"""
        if len(corners) < 8:
            return None
        
        # 转换为点数组
        points = []
        for i in range(0, len(corners), 2):
            if i + 1 < len(corners):
                points.append((corners[i], corners[i + 1]))
        
        if len(points) < 4:
            return None
        
        # 如果正好4个点，直接返回
        if len(points) == 4:
            return points
        
        # 如果超过4个点，使用简单的极值点选择
        points_array = np.array(points)
        
        # 找到极值点
        min_x_idx = np.argmin(points_array[:, 0])
        max_x_idx = np.argmax(points_array[:, 0])
        min_y_idx = np.argmin(points_array[:, 1])
        max_y_idx = np.argmax(points_array[:, 1])
        
        # 选择4个不同的极值点
        selected_indices = list(set([min_x_idx, max_x_idx, min_y_idx, max_y_idx]))
        
        # 如果不足4个，补充其他点
        while len(selected_indices) < 4 and len(selected_indices) < len(points):
            for i in range(len(points)):
                if i not in selected_indices:
                    selected_indices.append(i)
                    break
        
        # 返回选中的点
        return [points[i] for i in selected_indices[:4]]
    
    def generate_comparison_report(self, output_path: str = "comparison_report.txt"):
        """生成对比分析报告"""
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write("Adobe Scan vs Python实现 - 数据对比分析报告\n")
            f.write("=" * 50 + "\n\n")
            
            if self.adobe_data:
                f.write("Adobe Scan 数据分析:\n")
                f.write("-" * 30 + "\n")
                
                corner_results = self.adobe_data.get('cornerResults', [])
                f.write(f"检测结果数量: {len(corner_results)}\n\n")
                
                for i, result in enumerate(corner_results):
                    f.write(f"结果 {i+1}:\n")
                    f.write(f"  类型: {result.get('type')}\n")
                    f.write(f"  角点数量: {result.get('cornerCount')}\n")
                    
                    corners = result.get('corners', [])
                    if corners:
                        f.write("  角点坐标:\n")
                        for j in range(0, len(corners), 2):
                            if j + 1 < len(corners):
                                x, y = corners[j], corners[j + 1]
                                f.write(f"    点{j//2 + 1}: ({x:.4f}, {y:.4f})\n")
                    f.write("\n")
            
            f.write("\n分析建议:\n")
            f.write("-" * 30 + "\n")
            f.write("1. 检查Python脚本的角点选择算法\n")
            f.write("2. 对比模型输出的原始数据\n")
            f.write("3. 验证坐标系转换是否正确\n")
            f.write("4. 分析Adobe的角点排序规则\n")
        
        print(f"[报告] 对比分析报告已生成: {output_path}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Adobe Scan vs Python数据对比分析")
    parser.add_argument("--adobe-data", "-a", help="Frida捕获的Adobe数据JSON文件")
    parser.add_argument("--python-data", "-p", help="Python脚本输出数据文件")
    parser.add_argument("--image", "-i", help="测试图像路径")
    parser.add_argument("--output-dir", "-o", default="comparison_results", help="输出目录")
    
    args = parser.parse_args()
    
    analyzer = DataComparisonAnalyzer()
    
    # 加载数据
    if args.adobe_data and os.path.exists(args.adobe_data):
        analyzer.load_adobe_data(args.adobe_data)
    else:
        print("[提示] 请先使用Frida脚本捕获Adobe数据")
        print("使用命令: frida -U -f com.adobe.scan.android -l frida_adobe_scan_analysis.js")
    
    if args.python_data and os.path.exists(args.python_data):
        analyzer.load_python_data(args.python_data)
    
    # 分析数据
    if analyzer.adobe_data:
        analyzer.analyze_corner_differences()
        
        # 可视化对比
        if args.image and os.path.exists(args.image):
            analyzer.visualize_comparison(args.image, args.output_dir)
        
        # 生成报告
        analyzer.generate_comparison_report(
            os.path.join(args.output_dir, "comparison_report.txt"))
    
    print("\n[完成] 数据对比分析完成")
    print(f"结果保存在: {args.output_dir}")


if __name__ == "__main__":
    main()
