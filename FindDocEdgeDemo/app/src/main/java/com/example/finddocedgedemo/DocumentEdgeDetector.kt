package com.example.finddocedgedemo

import android.content.Context
import android.content.res.AssetManager
import android.graphics.Bitmap
import android.graphics.PointF
import android.util.Log
import org.tensorflow.lite.Interpreter
import java.io.FileInputStream
import java.nio.ByteBuffer
import java.nio.ByteOrder
import java.nio.MappedByteBuffer
import java.nio.channels.FileChannel

/**
 * Adobe Scan 文档边缘检测器
 * 基于逆向工程分析的 Adobe Scan 模型实现实时文档边缘检测
 */
class DocumentEdgeDetector(private val context: Context) {
    
    companion object {
        private const val TAG = "DocumentEdgeDetector"
        
        // 模型文件名（基于 Adobe Scan 分析结果）
        private const val CAPTURE_MODEL = "capture_6_22_23.tflite"  // 主要边缘检测模型 (2.42MB)
        private const val RESIZE_MODEL = "resize_to_320.tflite"     // 图像预处理模型 (7.8KB)
        private const val PAGE_TURN_MODEL = "page_turn_2im_002.tflite" // 页面方向检测模型 (1.16MB)
        
        // 输入图像尺寸（基于 Adobe Scan 分析）
        private const val INPUT_WIDTH = 640
        private const val INPUT_HEIGHT = 480
        private const val INPUT_CHANNELS = 1  // 灰度图像
        
        // 输出角点数量
        private const val OUTPUT_POINTS = 13  // 根据实际模型输出
        private const val CORNER_COORDINATES = 2  // x, y
        
        // 文档角点索引 (需要从13个点中选择4个角点)
        // 根据实际检测结果分析，重新映射角点索引
        private val CORNER_INDICES = intArrayOf(0, 1, 2, 4)  // 左上、右上、右下、左下
        
        // 加载本地库
        init {
//            System.loadLibrary("finddocedgedemo")
        }
    }
    
    // TensorFlow Lite 解释器
    private var captureInterpreter: Interpreter? = null
    private var resizeInterpreter: Interpreter? = null
    private var pageTurnInterpreter: Interpreter? = null
    
    // 输入输出缓冲区
    private var inputBuffer: ByteBuffer? = null
    private var outputBuffer: ByteBuffer? = null
    
    // 检测状态
    private var isInitialized = false
    private var useNativeDetection = false  // 禁用本地检测，改为使用Java实现
    private var testMode = false  // 测试模式，用于生成测试角点
    
    /**
     * 初始化模型
     */
    fun initialize(): Boolean {
        return try {
            Log.d(TAG, "开始初始化 Adobe Scan 文档边缘检测模型...")
            
            if (useNativeDetection) {
                // 使用本地方法初始化模型
                val modelPath = "models/$CAPTURE_MODEL"
                isInitialized = initModelNative(context.assets, modelPath)
                Log.d(TAG, "本地模型初始化${if (isInitialized) "成功" else "失败"}")
            } else {
                // 使用Java初始化模型
                // 加载主要的边缘检测模型
                val captureModel = loadModelFile(CAPTURE_MODEL)
                captureInterpreter = Interpreter(captureModel, getInterpreterOptions())
                
                // 加载图像预处理模型
                val resizeModel = loadModelFile(RESIZE_MODEL)
                resizeInterpreter = Interpreter(resizeModel, getInterpreterOptions())
                
                // 加载页面方向检测模型
                val pageTurnModel = loadModelFile(PAGE_TURN_MODEL)
                pageTurnInterpreter = Interpreter(pageTurnModel, getInterpreterOptions())
                
                // 初始化输入输出缓冲区
                initializeBuffers()
                
                isInitialized = true
                Log.d(TAG, "Adobe Scan 模型初始化成功")
                
                // 打印模型信息
                logModelInfo()
            }
            
            isInitialized
        } catch (e: Exception) {
            Log.e(TAG, "模型初始化失败", e)
            false
        }
    }
    
    /**
     * 检测文档边缘
     * @param bitmap 输入图像
     * @return 检测到的四个角点坐标（归一化坐标 0-1）
     */
    fun detectEdges(bitmap: Bitmap): Array<PointF>? {
        if (!isInitialized) {
            Log.w(TAG, "模型未初始化")
            return null
        }

        return try {
            Log.d(TAG, "开始文档边缘检测 - 输入图像尺寸: ${bitmap.width}x${bitmap.height}")

            // 测试模式：生成测试角点
            if (testMode) {
                Log.d(TAG, "测试模式：生成测试角点")
                return generateTestCorners()
            }

            if (useNativeDetection) {
                // 使用本地方法检测边缘
                // 1. 预处理图像
                val preprocessedBitmap = preprocessImage(bitmap)
                Log.d(TAG, "图像预处理完成 - 输出尺寸: ${preprocessedBitmap.width}x${preprocessedBitmap.height}")

                // 2. 转换为模型输入格式 (UINT8类型)
                val inputArray = bitmapToInputArray(preprocessedBitmap)
                Log.d(TAG, "输入数组转换完成 - 数组大小: ${inputArray.size}")

                // 3. 调用本地方法进行边缘检测
                val modelPath = "models/$CAPTURE_MODEL"
                val cornersArray = detectEdgesNative(
                    inputArray,
                    INPUT_WIDTH,
                    INPUT_HEIGHT,
                    context.assets,
                    modelPath
                )

                // 4. 转换结果为PointF数组
                val corners = Array(4) { PointF() }
                for (i in 0 until 4) {
                    corners[i].x = cornersArray[i * 2]
                    corners[i].y = cornersArray[i * 2 + 1]
                    Log.d(TAG, "角点[$i]: (${corners[i].x}, ${corners[i].y})")
                }

                corners
            } else {
                // 使用Java检测边缘
                // 1. 预处理图像
                val preprocessedBitmap = preprocessImage(bitmap)
                Log.d(TAG, "图像预处理完成 - 输出尺寸: ${preprocessedBitmap.width}x${preprocessedBitmap.height}")

                // 2. 转换为模型输入格式 (UINT8类型)
                val inputArray = bitmapToInputArray(preprocessedBitmap)
                Log.d(TAG, "输入数组转换完成 - 数组大小: ${inputArray.size}")

                // 3. 运行边缘检测模型
                val corners = runEdgeDetection(inputArray)
                Log.d(TAG, "模型推理完成 - 原始输出点数: ${corners?.size ?: 0}")

                // 4. 后处理结果
                val processedCorners = postprocessCorners(corners)

                Log.d(TAG, "边缘检测完成，最终检测到 ${processedCorners?.size ?: 0} 个角点")

                processedCorners
            }
        } catch (e: Exception) {
            Log.e(TAG, "边缘检测失败", e)
            e.printStackTrace()
            null
        }
    }
    
    // Native方法声明
    private external fun initModelNative(assetManager: AssetManager, modelPath: String): Boolean
    private external fun detectEdgesNative(
        inputArray: ByteArray, 
        width: Int, 
        height: Int, 
        assetManager: AssetManager, 
        modelPath: String
    ): FloatArray
    private external fun releaseModelNative()
    
    /**
     * 预处理图像
     */
    private fun preprocessImage(bitmap: Bitmap): Bitmap {
        Log.d(TAG, "开始图像预处理...")
        Log.d(TAG, "  - 原始图像尺寸: ${bitmap.width}x${bitmap.height}")
        Log.d(TAG, "  - 原始图像格式: ${bitmap.config}")
        Log.d(TAG, "  - 目标尺寸: ${INPUT_WIDTH}x${INPUT_HEIGHT}")

        // 1. 调整图像大小到模型输入尺寸
        val resizedBitmap = Bitmap.createScaledBitmap(bitmap, INPUT_WIDTH, INPUT_HEIGHT, true)
        Log.d(TAG, "  - 尺寸调整完成: ${resizedBitmap.width}x${resizedBitmap.height}")

        // 2. 转换为灰度图像
        val grayBitmap = convertToGrayscale(resizedBitmap)
        Log.d(TAG, "  - 灰度转换完成")

        // 3. 可选：图像增强
        val enhancedBitmap = enhanceImage(grayBitmap)
        Log.d(TAG, "  - 图像增强完成")

        return enhancedBitmap
    }

    /**
     * 转换为灰度图像
     */
    private fun convertToGrayscale(bitmap: Bitmap): Bitmap {
        val grayBitmap = Bitmap.createBitmap(bitmap.width, bitmap.height, Bitmap.Config.ARGB_8888)
        val canvas = android.graphics.Canvas(grayBitmap)
        val paint = android.graphics.Paint()

        // 使用标准的灰度转换矩阵 (ITU-R BT.709)
        val colorMatrix = android.graphics.ColorMatrix(floatArrayOf(
            0.2126f, 0.7152f, 0.0722f, 0f, 0f,  // Red
            0.2126f, 0.7152f, 0.0722f, 0f, 0f,  // Green
            0.2126f, 0.7152f, 0.0722f, 0f, 0f,  // Blue
            0f, 0f, 0f, 1f, 0f                   // Alpha
        ))

        paint.colorFilter = android.graphics.ColorMatrixColorFilter(colorMatrix)
        canvas.drawBitmap(bitmap, 0f, 0f, paint)

        return grayBitmap
    }

    /**
     * 图像增强 (可选)
     */
    private fun enhanceImage(bitmap: Bitmap): Bitmap {
        // 目前返回原图，后续可以添加对比度增强、直方图均衡化等
        return bitmap
    }
    
    /**
     * 将 Bitmap 转换为模型输入数组
     */
    private fun bitmapToInputArray(bitmap: Bitmap): ByteArray {
        Log.d(TAG, "开始转换 Bitmap 到输入数组...")

        val inputArray = ByteArray(INPUT_WIDTH * INPUT_HEIGHT * INPUT_CHANNELS)
        val pixels = IntArray(INPUT_WIDTH * INPUT_HEIGHT)

        bitmap.getPixels(pixels, 0, INPUT_WIDTH, 0, 0, INPUT_WIDTH, INPUT_HEIGHT)
        Log.d(TAG, "  - 提取像素完成，像素数量: ${pixels.size}")

        // 分析像素值分布
        var minGray = 255
        var maxGray = 0
        var sumGray = 0L

        for (i in pixels.indices) {
            // 提取灰度值 - 由于已经是灰度图，RGB三个通道应该相等
            val pixel = pixels[i]
            val red = (pixel shr 16) and 0xFF
            val green = (pixel shr 8) and 0xFF
            val blue = pixel and 0xFF

            // 使用加权平均计算灰度值 (ITU-R BT.709 标准)
            val gray = (0.2126 * red + 0.7152 * green + 0.0722 * blue).toInt()
            val grayByte = gray.coerceIn(0, 255).toByte()

            inputArray[i] = grayByte

            // 统计信息
            val grayInt = gray.coerceIn(0, 255)
            minGray = minOf(minGray, grayInt)
            maxGray = maxOf(maxGray, grayInt)
            sumGray += grayInt
        }

        val avgGray = sumGray / pixels.size
        Log.d(TAG, "  - 灰度值统计: 最小=${minGray}, 最大=${maxGray}, 平均=${avgGray}")
        Log.d(TAG, "  - 输入数组大小: ${inputArray.size} bytes")

        // 检查数据范围是否合理
        if (maxGray - minGray < 50) {
            Log.w(TAG, "  - 警告: 图像对比度较低，可能影响检测效果")
        }

        // 采样检查前几个像素值
        Log.d(TAG, "  - 前10个像素值: ${inputArray.take(10).map { it.toInt() and 0xFF }}")

        return inputArray
    }
    
    /**
     * 运行边缘检测模型
     */
    private fun runEdgeDetection(inputArray: ByteArray): Array<FloatArray>? {
        val interpreter = captureInterpreter ?: return null

        Log.d(TAG, "准备模型输入数据...")

        // 获取模型的实际输入格式
        val inputTensor = interpreter.getInputTensor(0)
        val inputDataType = inputTensor.dataType()
        val inputShape = inputTensor.shape()

        Log.d(TAG, "  - 模型输入数据类型: $inputDataType")
        Log.d(TAG, "  - 模型输入形状: ${inputShape.contentToString()}")

        try {
            val startTime = System.currentTimeMillis()

            // 根据模型的数据类型准备输入
            when (inputDataType) {
                org.tensorflow.lite.DataType.UINT8 -> {
                    Log.d(TAG, "  - 使用 UINT8 输入格式")
                    val input = Array(1) { Array(INPUT_HEIGHT) { Array(INPUT_WIDTH) { ByteArray(INPUT_CHANNELS) } } }
                    var index = 0
                    for (y in 0 until INPUT_HEIGHT) {
                        for (x in 0 until INPUT_WIDTH) {
                            input[0][y][x][0] = inputArray[index++]
                        }
                    }

                    // 准备输出
                    val output = Array(1) { Array(OUTPUT_POINTS) { FloatArray(CORNER_COORDINATES) } }

                    // 运行推理
                    interpreter.run(input, output)

                    val inferenceTime = System.currentTimeMillis() - startTime
                    Log.d(TAG, "模型推理成功 (UINT8)，耗时: ${inferenceTime}ms")

                    return processModelOutput(output[0])
                }

                org.tensorflow.lite.DataType.FLOAT32 -> {
                    Log.d(TAG, "  - 使用 FLOAT32 输入格式")
                    val input = Array(1) { Array(INPUT_HEIGHT) { Array(INPUT_WIDTH) { FloatArray(INPUT_CHANNELS) } } }
                    var index = 0
                    for (y in 0 until INPUT_HEIGHT) {
                        for (x in 0 until INPUT_WIDTH) {
                            // 将字节值 (0-255) 归一化到 (0.0-1.0)
                            val byteValue = inputArray[index++].toInt() and 0xFF
                            input[0][y][x][0] = byteValue / 255.0f
                        }
                    }

                    // 准备输出
                    val output = Array(1) { Array(OUTPUT_POINTS) { FloatArray(CORNER_COORDINATES) } }

                    // 运行推理
                    interpreter.run(input, output)

                    val inferenceTime = System.currentTimeMillis() - startTime
                    Log.d(TAG, "模型推理成功 (FLOAT32)，耗时: ${inferenceTime}ms")

                    return processModelOutput(output[0])
                }

                else -> {
                    Log.e(TAG, "不支持的输入数据类型: $inputDataType")
                    return null
                }
            }

        } catch (e: Exception) {
            Log.e(TAG, "模型推理失败", e)
            e.printStackTrace()
            return null
        }
    }

    /**
     * 处理模型输出
     */
    private fun processModelOutput(output: Array<FloatArray>): Array<FloatArray> {
        Log.d(TAG, "=== 模型输出详细信息 ===")
        var validPoints = 0
        var extremePoints = 0

        for (i in output.indices) {
            val x = output[i][0]
            val y = output[i][1]
            Log.d(TAG, "检测点[$i]: (${String.format("%.6f", x)}, ${String.format("%.6f", y)})")

            // 检查坐标是否在合理范围内
            if (x >= 0f && x <= 1f && y >= 0f && y <= 1f) {
                validPoints++
            }

            // 检查是否是极端值 (可能表示边界点)
            if (x <= 0.01f || x >= 0.99f || y <= 0.01f || y >= 0.99f) {
                extremePoints++
            }
        }

        Log.d(TAG, "有效检测点数量: $validPoints / ${output.size}")
        Log.d(TAG, "边界检测点数量: $extremePoints / ${output.size}")
        Log.d(TAG, "========================")

        return output
    }
    
    /**
     * 后处理角点坐标
     */
    private fun postprocessCorners(corners: Array<FloatArray>?): Array<PointF>? {
        if (corners == null) {
            Log.w(TAG, "输入角点数组为空")
            return null
        }

        try {
            Log.d(TAG, "开始后处理角点坐标...")
            Log.d(TAG, "输入检测点数量: ${corners.size}")

            // 智能选择四个角点
            val selectedCorners = selectBestCorners(corners)

            if (selectedCorners != null) {
                // 验证角点的合理性
                val isValidDocument = validateDocumentCorners(selectedCorners)
                Log.d(TAG, "文档角点验证结果: ${if (isValidDocument) "有效" else "无效"}")
                return selectedCorners
            } else {
                Log.w(TAG, "无法选择合适的角点")
                return null
            }
        } catch (e: Exception) {
            Log.e(TAG, "处理角点失败", e)
            e.printStackTrace()
            return null
        }
    }

    /**
     * 智能选择最佳的四个角点
     */
    private fun selectBestCorners(corners: Array<FloatArray>): Array<PointF>? {
        if (corners.size < 4) {
            Log.w(TAG, "检测点数量不足: ${corners.size}")
            return null
        }

        // 将所有检测点转换为PointF
        val allPoints = corners.mapIndexed { index, corner ->
            if (corner.size >= 2) {
                IndexedPoint(index, PointF(corner[0], corner[1]))
            } else {
                null
            }
        }.filterNotNull()

        Log.d(TAG, "有效检测点数量: ${allPoints.size}")

        // 使用更智能的角点选择算法
        return selectCornersUsingGeometry(allPoints)
    }

    /**
     * 基于几何距离的角点选择算法
     */
    private fun selectCornersUsingGeometry(points: List<IndexedPoint>): Array<PointF> {
        Log.d(TAG, "使用几何距离算法选择角点...")

        // 定义四个理想角点位置
        val idealCorners = arrayOf(
            PointF(0.0f, 0.0f),  // 左上
            PointF(1.0f, 0.0f),  // 右上
            PointF(1.0f, 1.0f),  // 右下
            PointF(0.0f, 1.0f)   // 左下
        )

        val selectedCorners = Array(4) { PointF() }
        val selectedIndices = Array(4) { -1 }

        // 为每个理想角点找到最近的检测点
        for (i in idealCorners.indices) {
            val idealCorner = idealCorners[i]
            var minDistance = Float.MAX_VALUE
            var bestPoint: IndexedPoint? = null

            for (point in points) {
                val distance = calculateDistance(point.point, idealCorner)
                if (distance < minDistance) {
                    minDistance = distance
                    bestPoint = point
                }
            }

            if (bestPoint != null) {
                selectedCorners[i] = bestPoint.point
                selectedIndices[i] = bestPoint.index
                Log.d(TAG, "  ${getCornerName(i)}[${bestPoint.index}]: (${String.format("%.6f", bestPoint.point.x)}, ${String.format("%.6f", bestPoint.point.y)}) 距离=${String.format("%.6f", minDistance)}")
            } else {
                // 如果没有找到合适的点，使用默认值
                selectedCorners[i] = idealCorners[i]
                Log.w(TAG, "  ${getCornerName(i)}: 使用默认值 (${idealCorners[i].x}, ${idealCorners[i].y})")
            }
        }

        // 验证选择的角点是否形成合理的四边形
        val isValid = validateSelectedCorners(selectedCorners)
        Log.d(TAG, "角点选择验证: ${if (isValid) "通过" else "失败"}")

        if (!isValid) {
            Log.w(TAG, "角点选择验证失败，尝试备用算法...")
            return selectCornersUsingConvexHull(points)
        }

        return selectedCorners
    }

    /**
     * 计算两点之间的欧几里得距离
     */
    private fun calculateDistance(p1: PointF, p2: PointF): Float {
        val dx = p1.x - p2.x
        val dy = p1.y - p2.y
        return kotlin.math.sqrt(dx * dx + dy * dy)
    }

    /**
     * 获取角点名称
     */
    private fun getCornerName(index: Int): String {
        return when (index) {
            0 -> "左上角"
            1 -> "右上角"
            2 -> "右下角"
            3 -> "左下角"
            else -> "未知角"
        }
    }

    /**
     * 验证选择的角点是否合理
     */
    private fun validateSelectedCorners(corners: Array<PointF>): Boolean {
        if (corners.size != 4) return false

        // 检查是否有重复的点
        for (i in corners.indices) {
            for (j in i + 1 until corners.size) {
                val distance = calculateDistance(corners[i], corners[j])
                if (distance < 0.1f) {
                    Log.w(TAG, "角点${i}和角点${j}距离过近: $distance")
                    return false
                }
            }
        }

        // 检查是否形成合理的四边形（简单检查面积）
        val area = calculateQuadrilateralArea(corners)
        Log.d(TAG, "四边形面积: ${String.format("%.6f", area)}")

        return area > 0.01f  // 面积应该大于1%
    }

    /**
     * 计算四边形面积（使用鞋带公式）
     */
    private fun calculateQuadrilateralArea(corners: Array<PointF>): Float {
        if (corners.size != 4) return 0f

        var area = 0f
        for (i in corners.indices) {
            val j = (i + 1) % corners.size
            area += corners[i].x * corners[j].y
            area -= corners[j].x * corners[i].y
        }
        return kotlin.math.abs(area) / 2f
    }

    /**
     * 备用算法：使用凸包选择角点
     */
    private fun selectCornersUsingConvexHull(points: List<IndexedPoint>): Array<PointF> {
        Log.d(TAG, "使用凸包算法选择角点...")

        // 简化版：选择最极端的4个点
        val leftMost = points.minByOrNull { it.point.x }
        val rightMost = points.maxByOrNull { it.point.x }
        val topMost = points.minByOrNull { it.point.y }
        val bottomMost = points.maxByOrNull { it.point.y }

        val corners = Array(4) { PointF() }
        corners[0] = leftMost?.point ?: PointF(0f, 0f)
        corners[1] = rightMost?.point ?: PointF(1f, 0f)
        corners[2] = rightMost?.point ?: PointF(1f, 1f)
        corners[3] = bottomMost?.point ?: PointF(0f, 1f)

        Log.d(TAG, "凸包算法选择结果:")
        corners.forEachIndexed { index, point ->
            Log.d(TAG, "  ${getCornerName(index)}: (${String.format("%.6f", point.x)}, ${String.format("%.6f", point.y)})")
        }

        return corners
    }

    /**
     * 带索引的点数据类
     */
    private data class IndexedPoint(val index: Int, val point: PointF)

    /**
     * 验证文档角点的合理性
     */
    private fun validateDocumentCorners(corners: Array<PointF>): Boolean {
        if (corners.size != 4) return false

        // 检查所有角点是否在有效范围内
        for (corner in corners) {
            if (corner.x < 0f || corner.x > 1f || corner.y < 0f || corner.y > 1f) {
                Log.w(TAG, "角点超出有效范围: (${corner.x}, ${corner.y})")
                return false
            }
        }

        // 检查角点是否形成合理的四边形
        // 这里可以添加更多的几何验证逻辑

        return true
    }
    
    /**
     * 加载模型文件
     */
    private fun loadModelFile(modelName: String): MappedByteBuffer {
        val assetFileDescriptor = context.assets.openFd("models/$modelName")
        val fileInputStream = FileInputStream(assetFileDescriptor.fileDescriptor)
        val fileChannel = fileInputStream.channel
        val startOffset = assetFileDescriptor.startOffset
        val declaredLength = assetFileDescriptor.declaredLength
        
        Log.d(TAG, "加载模型: $modelName, 大小: ${declaredLength / 1024 / 1024}MB")
        
        return fileChannel.map(FileChannel.MapMode.READ_ONLY, startOffset, declaredLength)
    }
    
    /**
     * 获取解释器选项
     */
    private fun getInterpreterOptions(): Interpreter.Options {
        val options = Interpreter.Options()
        
        // 设置线程数
        options.setNumThreads(4)
        
        // 禁用GPU加速，只使用CPU
        // 原代码中尝试使用GPU代理但找不到相关类，所以直接使用CPU模式
        Log.d(TAG, "使用CPU模式运行TensorFlow Lite模型")
        
        return options
    }
    
    /**
     * 初始化缓冲区
     */
    private fun initializeBuffers() {
        // 输入缓冲区
        val inputSize = INPUT_WIDTH * INPUT_HEIGHT * INPUT_CHANNELS * 4 // Float32
        inputBuffer = ByteBuffer.allocateDirect(inputSize)
        inputBuffer?.order(ByteOrder.nativeOrder())
        
        // 输出缓冲区
        val outputSize = OUTPUT_POINTS * CORNER_COORDINATES * 4 // Float32
        outputBuffer = ByteBuffer.allocateDirect(outputSize)
        outputBuffer?.order(ByteOrder.nativeOrder())
    }
    
    /**
     * 打印模型信息
     */
    private fun logModelInfo() {
        captureInterpreter?.let { interpreter ->
            try {
                Log.d(TAG, "=== Adobe Scan 边缘检测模型详细信息 ===")

                // 打印所有输入张量信息
                Log.d(TAG, "模型输入张量数量: ${interpreter.inputTensorCount}")
                for (i in 0 until interpreter.inputTensorCount) {
                    val inputTensor = interpreter.getInputTensor(i)
                    val shape = inputTensor.shape()
                    Log.d(TAG, "输入张量[$i] 详细信息:")
                    Log.d(TAG, "  - 形状: ${shape.contentToString()}")
                    Log.d(TAG, "  - 数据类型: ${inputTensor.dataType()}")
                    Log.d(TAG, "  - 名称: ${inputTensor.name()}")
                    Log.d(TAG, "  - 字节数: ${inputTensor.numBytes()}")
                    Log.d(TAG, "  - 元素数量: ${inputTensor.numElements()}")

                    // 分析输入格式
                    if (shape.size == 4) {
                        Log.d(TAG, "  - 批次大小: ${shape[0]}")
                        Log.d(TAG, "  - 高度: ${shape[1]}")
                        Log.d(TAG, "  - 宽度: ${shape[2]}")
                        Log.d(TAG, "  - 通道数: ${shape[3]}")
                        Log.d(TAG, "  - 格式: NHWC (Batch, Height, Width, Channels)")
                    }

                    try {
                        val quantParams = inputTensor.quantizationParams()
                        Log.d(TAG, "  - 量化参数: 缩放=${quantParams.scale}, 零点=${quantParams.zeroPoint}")
                        if (quantParams.scale != 0f) {
                            Log.d(TAG, "  - 量化范围: [${quantParams.zeroPoint * quantParams.scale}, ${(255 + quantParams.zeroPoint) * quantParams.scale}]")
                        }
                    } catch (e: Exception) {
                        Log.d(TAG, "  - 量化参数: 无 (浮点模型)")
                    }
                }

                // 打印所有输出张量信息
                Log.d(TAG, "模型输出张量数量: ${interpreter.outputTensorCount}")
                for (i in 0 until interpreter.outputTensorCount) {
                    val outputTensor = interpreter.getOutputTensor(i)
                    val shape = outputTensor.shape()
                    Log.d(TAG, "输出张量[$i] 详细信息:")
                    Log.d(TAG, "  - 形状: ${shape.contentToString()}")
                    Log.d(TAG, "  - 数据类型: ${outputTensor.dataType()}")
                    Log.d(TAG, "  - 名称: ${outputTensor.name()}")
                    Log.d(TAG, "  - 字节数: ${outputTensor.numBytes()}")
                    Log.d(TAG, "  - 元素数量: ${outputTensor.numElements()}")

                    // 分析输出格式
                    if (shape.size == 3) {
                        Log.d(TAG, "  - 批次大小: ${shape[0]}")
                        Log.d(TAG, "  - 检测点数: ${shape[1]}")
                        Log.d(TAG, "  - 坐标维度: ${shape[2]}")
                        Log.d(TAG, "  - 格式: BNC (Batch, NumPoints, Coordinates)")
                    }

                    try {
                        val quantParams = outputTensor.quantizationParams()
                        Log.d(TAG, "  - 量化参数: 缩放=${quantParams.scale}, 零点=${quantParams.zeroPoint}")
                    } catch (e: Exception) {
                        Log.d(TAG, "  - 量化参数: 无 (浮点模型)")
                    }
                }

                // 打印当前配置与模型的匹配情况
                val inputShape = interpreter.getInputTensor(0).shape()
                val outputShape = interpreter.getOutputTensor(0).shape()

                Log.d(TAG, "配置匹配检查:")
                Log.d(TAG, "  - 当前输入配置: ${INPUT_WIDTH}x${INPUT_HEIGHT}x${INPUT_CHANNELS}")
                Log.d(TAG, "  - 模型期望输入: ${inputShape[2]}x${inputShape[1]}x${inputShape[3]}")
                Log.d(TAG, "  - 输入匹配: ${inputShape[1] == INPUT_HEIGHT && inputShape[2] == INPUT_WIDTH && inputShape[3] == INPUT_CHANNELS}")
                Log.d(TAG, "  - 当前输出配置: ${OUTPUT_POINTS} 点")
                Log.d(TAG, "  - 模型实际输出: ${outputShape[1]} 点")
                Log.d(TAG, "  - 输出匹配: ${outputShape[1] == OUTPUT_POINTS}")

                Log.d(TAG, "模型版本: Adobe Scan capture_6_22_23")
                Log.d(TAG, "模型文件大小: ~2.42MB")
                Log.d(TAG, "============================================")
            } catch (e: Exception) {
                Log.e(TAG, "获取模型信息失败", e)
                e.printStackTrace()
            }
        }
    }
    
    /**
     * 释放资源
     */
    fun release() {
        if (useNativeDetection) {
            // 释放本地资源
            releaseModelNative()
        } else {
            // 释放Java资源
            captureInterpreter?.close()
            resizeInterpreter?.close()
            pageTurnInterpreter?.close()
            
            captureInterpreter = null
            resizeInterpreter = null
            pageTurnInterpreter = null
        }
        
        isInitialized = false
        Log.d(TAG, "模型资源已释放")
    }
    
    /**
     * 生成测试角点（用于测试可视化功能）
     */
    private fun generateTestCorners(): Array<PointF> {
        val corners = Array(4) { PointF() }

        // 生成一个模拟的文档矩形
        corners[0] = PointF(0.2f, 0.3f)  // 左上
        corners[1] = PointF(0.8f, 0.25f) // 右上
        corners[2] = PointF(0.85f, 0.75f) // 右下
        corners[3] = PointF(0.15f, 0.8f) // 左下

        Log.d(TAG, "生成测试角点:")
        corners.forEachIndexed { index, point ->
            Log.d(TAG, "  测试角点[$index]: (${point.x}, ${point.y})")
        }

        return corners
    }

    /**
     * 启用/禁用测试模式
     */
    fun setTestMode(enabled: Boolean) {
        testMode = enabled
        Log.d(TAG, "测试模式: ${if (enabled) "启用" else "禁用"}")
    }

    /**
     * 检查是否已初始化
     */
    fun isReady(): Boolean = isInitialized
}
