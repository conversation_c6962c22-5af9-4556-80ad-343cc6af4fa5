#!/bin/bash

# Adobe Scan 文档边缘检测演示项目构建脚本

echo "🚀 开始构建 Adobe Scan 文档边缘检测演示项目..."

# 检查 Android SDK
if [ -z "$ANDROID_HOME" ]; then
    echo "❌ 错误: ANDROID_HOME 环境变量未设置"
    echo "请设置 Android SDK 路径，例如:"
    echo "export ANDROID_HOME=/path/to/android/sdk"
    exit 1
fi

echo "✅ Android SDK 路径: $ANDROID_HOME"

# 检查模型文件
echo "📋 检查 Adobe Scan 模型文件..."
MODEL_DIR="app/src/main/assets/models"

if [ ! -d "$MODEL_DIR" ]; then
    echo "❌ 错误: 模型目录不存在: $MODEL_DIR"
    exit 1
fi

MODELS=(
    "capture_6_22_23.tflite"
    "page_turn_2im_002.tflite"
    "resize_to_320.tflite"
)

for model in "${MODELS[@]}"; do
    if [ -f "$MODEL_DIR/$model" ]; then
        size=$(du -h "$MODEL_DIR/$model" | cut -f1)
        echo "✅ $model ($size)"
    else
        echo "❌ 缺失模型文件: $model"
        exit 1
    fi
done

# 清理项目
echo "🧹 清理项目..."
./gradlew clean

# 构建项目
echo "🔨 构建项目..."
./gradlew assembleDebug

if [ $? -eq 0 ]; then
    echo "✅ 项目构建成功！"
    
    # 显示 APK 信息
    APK_PATH="app/build/outputs/apk/debug/app-debug.apk"
    if [ -f "$APK_PATH" ]; then
        size=$(du -h "$APK_PATH" | cut -f1)
        echo "📱 APK 文件: $APK_PATH ($size)"
        
        # 检查 APK 中的模型文件
        echo "📋 验证 APK 中的模型文件..."
        unzip -l "$APK_PATH" | grep "\.tflite$" || echo "⚠️  APK 中未找到 .tflite 文件"
    fi
    
    echo ""
    echo "🎉 Adobe Scan 文档边缘检测演示项目构建完成！"
    echo ""
    echo "📱 功能特性:"
    echo "   ✅ 实时相机预览 (CameraX)"
    echo "   ✅ Adobe Scan AI 模型集成"
    echo "   ✅ 实时文档边缘检测"
    echo "   ✅ 四角点坐标识别"
    echo "   ✅ 可视化检测结果"
    echo ""
    echo "🔧 技术栈:"
    echo "   • TensorFlow Lite 2.14.0"
    echo "   • CameraX 1.3.1"
    echo "   • Adobe Scan capture_6_22_23.tflite (2.42MB)"
    echo "   • Kotlin + Android NDK"
    echo ""
    echo "📖 使用说明:"
    echo "   1. 安装 APK 到设备"
    echo "   2. 授予相机权限"
    echo "   3. 将文档放入扫描框"
    echo "   4. 等待边缘检测完成"
    echo "   5. 点击拍照按钮捕获文档"
    echo ""
    echo "🚀 安装命令:"
    echo "   adb install $APK_PATH"

    # 检查是否有连接的设备
    adb_devices=$(adb devices | grep -v "List of devices" | grep "device$" | wc -l)

    if [ $adb_devices -gt 0 ]; then
        echo ""
        echo "📱 发现 $adb_devices 个连接的设备"
        echo "🚀 是否要安装应用? (y/n)"
        read -r install_choice

        if [ "$install_choice" = "y" ] || [ "$install_choice" = "Y" ]; then
            echo "📲 安装应用..."
            adb install -r "$APK_PATH"
            echo "✅ 应用安装完成!"

            echo "🎯 是否要启动应用? (y/n)"
            read -r launch_choice

            if [ "$launch_choice" = "y" ] || [ "$launch_choice" = "Y" ]; then
                echo "🚀 启动应用..."
                adb shell am start -n com.example.finddocedgedemo/.MainActivity
                echo "✅ 应用已启动!"

                echo ""
                echo "💡 使用提示:"
                echo "   • 长按右上角信息按钮启用测试模式"
                echo "   • 测试模式会显示预设的测试角点"
                echo "   • 观察日志了解模型推理过程"
                echo ""

                echo "📋 查看日志? (y/n)"
                read -r log_choice

                if [ "$log_choice" = "y" ] || [ "$log_choice" = "Y" ]; then
                    echo "📋 显示应用日志 (按 Ctrl+C 停止)..."
                    echo "🔍 过滤关键词: DocumentEdgeDetector, MainActivity, CameraManager, DocumentScannerView"
                    echo "----------------------------------------"
                    adb logcat | grep -E "(DocumentEdgeDetector|MainActivity|CameraManager|DocumentScannerView)"
                fi
            fi
        fi
    else
        echo ""
        echo "📱 未发现连接的设备"
        echo "💡 请连接 Android 设备或启动模拟器后重新运行脚本"
    fi

else
    echo "❌ 项目构建失败！"
    echo "请检查错误信息并修复问题。"
    exit 1
fi
