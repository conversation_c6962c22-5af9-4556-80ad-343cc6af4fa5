# 📋 FindDocEdgeDemo 测试指南

## 🎯 项目概述

FindDocEdgeDemo 是基于 Adobe Scan 逆向工程分析的实时文档边缘检测演示应用，集成了 Adobe Scan 的核心 AI 模型，实现了与原版相同的文档检测功能。

## 🚀 快速开始

### 1. 构建项目

```bash
cd FindDocEdgeDemo
chmod +x build_and_test.sh
./build_and_test.sh
```

### 2. 手动安装

```bash
# 构建 APK
./gradlew assembleDebug

# 安装到设备
adb install app/build/outputs/apk/debug/app-debug.apk

# 启动应用
adb shell am start -n com.example.finddocedgedemo/.MainActivity
```

## 📱 功能测试

### 基本功能测试

1. **权限测试**
   - 启动应用后应自动请求相机权限
   - 授予权限后相机预览应正常显示

2. **模型初始化测试**
   - 观察状态栏显示"正在初始化模型..."
   - 初始化成功后显示"模型加载成功，开始检测..."

3. **实时检测测试**
   - 将文档放入相机视野
   - 观察是否检测到文档边缘
   - 检测成功时应显示绿色边框和角点

### 测试模式

**启用测试模式：**
- 长按右上角信息按钮
- 应显示"测试模式已启用"提示
- 屏幕上会显示预设的测试角点

**测试角点坐标：**
- 左上角: (0.2, 0.3)
- 右上角: (0.8, 0.25)
- 右下角: (0.85, 0.75)
- 左下角: (0.15, 0.8)

## 🔍 日志分析

### 查看实时日志

```bash
adb logcat | grep -E "(DocumentEdgeDetector|MainActivity|CameraManager|DocumentScannerView)"
```

### 关键日志信息

#### 1. 模型初始化日志

```
DocumentEdgeDetector: 开始初始化 Adobe Scan 文档边缘检测模型...
DocumentEdgeDetector: 加载模型: capture_6_22_23.tflite, 大小: 2MB
DocumentEdgeDetector: 使用CPU模式运行TensorFlow Lite模型
DocumentEdgeDetector: Adobe Scan 模型初始化成功
```

#### 2. 模型信息日志

```
DocumentEdgeDetector: === Adobe Scan 边缘检测模型信息 ===
DocumentEdgeDetector: 模型输入张量数量: 1
DocumentEdgeDetector: 输入张量[0] 形状: [1, 480, 640, 1]
DocumentEdgeDetector: 输入张量[0] 数据类型: UINT8
DocumentEdgeDetector: 模型输出张量数量: 1
DocumentEdgeDetector: 输出张量[0] 形状: [1, 13, 2]
DocumentEdgeDetector: 输出张量[0] 数据类型: FLOAT32
```

#### 3. 检测过程日志

```
DocumentEdgeDetector: 开始文档边缘检测 - 输入图像尺寸: 640x480
DocumentEdgeDetector: 图像预处理完成 - 输出尺寸: 640x480
DocumentEdgeDetector: 输入数组转换完成 - 数组大小: 307200
DocumentEdgeDetector: 准备模型输入数据...
DocumentEdgeDetector: 输入数据准备完成，开始模型推理...
DocumentEdgeDetector: 模型推理成功，耗时: 45ms，输出形状: [1, 13, 2]
```

#### 4. 检测结果日志

```
DocumentEdgeDetector: === 模型输出详细信息 ===
DocumentEdgeDetector: 检测点[0]: (0.234567, 0.345678)
DocumentEdgeDetector: 检测点[1]: (0.456789, 0.567890)
...
DocumentEdgeDetector: 有效检测点数量: 13 / 13
DocumentEdgeDetector: 开始后处理角点坐标...
DocumentEdgeDetector: 角点索引映射: [0, 3, 9, 6]
DocumentEdgeDetector: 角点[0] (来自检测点[0]): 原始(0.234567, 0.345678) -> 处理后(0.234567, 0.345678)
```

## 🐛 常见问题排查

### 1. 模型加载失败

**症状：** 显示"模型加载失败"
**原因：** 模型文件缺失或损坏
**解决：** 检查 `app/src/main/assets/models/` 目录下的模型文件

### 2. 相机权限问题

**症状：** 黑屏或权限错误
**原因：** 相机权限未授予
**解决：** 在设置中手动授予相机权限

### 3. 检测不到文档

**症状：** 一直显示"寻找文档..."
**原因：** 光线不足、文档不清晰或模型推理问题
**解决：** 
- 改善光线条件
- 使用清晰的文档
- 启用测试模式验证可视化功能

### 4. 应用崩溃

**症状：** 应用启动后立即崩溃
**原因：** 可能是 TensorFlow Lite 版本不兼容
**解决：** 查看崩溃日志，检查依赖版本

## 📊 性能监控

### 推理性能指标

- **推理时间：** 通常在 30-80ms 之间
- **内存占用：** 约 15-20MB
- **CPU 使用率：** 检测时约 20-40%

### 性能优化建议

1. **降低检测频率：** 如果性能不足，可以降低图像分析频率
2. **使用 GPU 加速：** 在支持的设备上启用 GPU 代理
3. **图像预处理优化：** 优化图像转换和预处理流程

## 🔧 开发调试

### 修改检测参数

在 `DocumentEdgeDetector.kt` 中可以调整：

```kotlin
// 角点索引映射 (从13个检测点中选择4个角点)
private val CORNER_INDICES = intArrayOf(0, 3, 9, 6)  // 左上、右上、右下、左下
```

### 启用详细日志

设置日志级别为 DEBUG：

```bash
adb shell setprop log.tag.DocumentEdgeDetector DEBUG
```

### 模型输出分析

模型输出 13 个检测点，每个点包含 (x, y) 坐标：
- 坐标范围：0.0 - 1.0 (归一化坐标)
- 角点选择：通过 CORNER_INDICES 数组选择 4 个角点
- 坐标验证：检查是否在有效范围内

## 📈 测试结果分析

### 成功指标

1. **模型加载成功率：** 应达到 100%
2. **检测响应时间：** < 100ms
3. **角点准确性：** 与实际文档边缘误差 < 5%
4. **稳定性：** 连续运行 30 分钟无崩溃

### 测试报告模板

```
测试时间：2024-XX-XX
设备型号：XXX
Android 版本：XX
测试结果：
- 模型加载：✅/❌
- 实时检测：✅/❌
- 角点精度：✅/❌
- 性能表现：✅/❌
- 稳定性：✅/❌

问题记录：
1. XXX
2. XXX

改进建议：
1. XXX
2. XXX
```

## 🎯 下一步计划

1. **模型优化：** 分析模型输出，优化角点选择算法
2. **性能提升：** 启用 GPU 加速，优化推理流程
3. **功能扩展：** 添加文档拍摄和透视校正功能
4. **用户体验：** 改进 UI 交互和视觉反馈

---

**📞 技术支持**
如有问题，请查看日志输出并参考本指南进行排查。
